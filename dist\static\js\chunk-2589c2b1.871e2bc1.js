(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2589c2b1"],{"73ca":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-container"},[t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(e.isEdit?"编辑账号":"新增账号"))])]),t("el-form",{ref:"accountForm",attrs:{model:e.accountForm,rules:e.rules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"用户名",prop:"username"}},[t("el-input",{attrs:{placeholder:"请输入用户名",disabled:e.isEdit},model:{value:e.accountForm.username,callback:function(t){e.$set(e.accountForm,"username",t)},expression:"accountForm.username"}})],1),e.isEdit?e._e():t("el-form-item",{attrs:{label:"密码",prop:"password"}},[t("el-input",{attrs:{placeholder:"请输入密码",type:"password"},model:{value:e.accountForm.password,callback:function(t){e.$set(e.accountForm,"password",t)},expression:"accountForm.password"}})],1),e.isEdit?e._e():t("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[t("el-input",{attrs:{placeholder:"请再次输入密码",type:"password"},model:{value:e.accountForm.confirmPassword,callback:function(t){e.$set(e.accountForm,"confirmPassword",t)},expression:"accountForm.confirmPassword"}})],1),t("el-form-item",{attrs:{label:"真实姓名",prop:"real_name"}},[t("el-input",{attrs:{placeholder:"请输入真实姓名"},model:{value:e.accountForm.real_name,callback:function(t){e.$set(e.accountForm,"real_name",t)},expression:"accountForm.real_name"}})],1),t("el-form-item",{attrs:{label:"头像",prop:"avatar"}},[t("el-upload",{staticClass:"avatar-uploader",attrs:{action:"#","show-file-list":!1,"http-request":e.uploadAvatar,"before-upload":e.beforeAvatarUpload}},[e.accountForm.avatar?t("img",{staticClass:"avatar",attrs:{src:e.accountForm.avatar}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),t("el-form-item",{attrs:{label:"角色",prop:"role_id"}},[t("el-select",{attrs:{placeholder:"请选择角色"},model:{value:e.accountForm.role_id,callback:function(t){e.$set(e.accountForm,"role_id",t)},expression:"accountForm.role_id"}},e._l(e.roleOptions,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:e.createNewRole}},[e._v("创建新角色")])],1),t("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[t("el-input",{attrs:{placeholder:"请输入邮箱"},model:{value:e.accountForm.email,callback:function(t){e.$set(e.accountForm,"email",t)},expression:"accountForm.email"}})],1),t("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[t("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:e.accountForm.phone,callback:function(t){e.$set(e.accountForm,"phone",t)},expression:"accountForm.phone"}})],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},model:{value:e.accountForm.status,callback:function(t){e.$set(e.accountForm,"status",t)},expression:"accountForm.status"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("提交")]),t("el-button",{on:{click:e.goBack}},[e._v("返回")])],1)],1)],1)],1)},r=[],s=(a("d9e2"),a("14d9"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("8593")),n=a("91b6"),i={name:"AccountForm",data(){const e=(e,t,a)=>{t!==this.accountForm.password?a(new Error("两次输入的密码不一致")):a()};return{isEdit:!1,accountId:void 0,accountForm:{username:"",password:"",confirmPassword:"",real_name:"",avatar:"",role_id:"",email:"",phone:"",status:1},roleOptions:[],rules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:4,max:20,message:"长度在 4 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入密码",trigger:"blur"},{validator:e,trigger:"blur"}],real_name:[{required:!0,message:"请输入真实姓名",trigger:"blur"}],role_id:[{required:!0,message:"请选择角色",trigger:"change"}],email:[{pattern:/^[\w.-]+@[\w.-]+\.\w+$/,message:"请输入有效的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号",trigger:"blur"}]}}},created(){this.getRoleOptions();const e=this.$route.params&&this.$route.params.id;e&&(this.isEdit=!0,this.accountId=e,this.getAccountDetail(e))},activated(){this.getRoleOptions()},beforeRouteEnter(e,t,a){a(e=>{"/system/role/create"===t.path&&(console.log("从角色创建页面返回，强制刷新角色列表"),e.getRoleOptions())})},methods:{getRoleOptions(){try{const e=localStorage.getItem("wifi_admin_roles");if(e){const t=JSON.parse(e);this.roleOptions=t||[],console.log("从localStorage获取的角色列表:",this.roleOptions)}}catch(e){console.error("从localStorage获取角色列表失败:",e)}Object(s["i"])().then(e=>{200===e.code?(this.roleOptions=e.data.list||[],console.log("通过API获取的角色列表:",this.roleOptions)):this.$message.error(e.message||"获取角色列表失败")}).catch(e=>{this.$message.error(e.message||"获取角色列表失败")})},getAccountDetail(e){console.log("获取账号详情，ID:",e);try{const t=localStorage.getItem("wifi_admin_accounts");if(console.log("从localStorage获取的原始账号数据:",t),t){const a=JSON.parse(t);console.log("解析后的账号数据:",a);const o=parseInt(e);console.log("查找账号ID:",o,"类型:",typeof o),a.forEach(e=>{console.log("账号ID:",e.id,"类型:",typeof e.id)});const r=a.find(e=>parseInt(e.id)===o);if(console.log("找到的账号:",r),r)return this.accountForm={id:r.id,username:r.username,real_name:r.real_name||r.nickname,avatar:r.avatar||"",role_id:r.role_id,email:r.email||"",phone:r.phone||"",status:r.status},void console.log("设置表单数据成功:",this.accountForm);console.error("在localStorage中未找到ID为",e,"的账号")}}catch(t){console.error("从localStorage获取账号详情失败:",t)}console.log("从API获取账号详情"),Object(s["e"])().then(t=>{if(200===t.code){const a=t.data.list||[],o=a.find(t=>parseInt(t.id)===parseInt(e));o?(this.accountForm={id:o.id,username:o.username,real_name:o.real_name||o.nickname,avatar:o.avatar||"",role_id:o.role_id,email:o.email||"",phone:o.phone||"",status:o.status},console.log("从API获取账号详情成功:",this.accountForm)):(console.error("API返回数据中未找到ID为",e,"的账号"),this.$message.error("账号不存在"),this.goBack())}}).catch(e=>{console.error("获取账号详情API调用失败:",e),this.$message.error(e.message||"获取账号详情失败"),this.goBack()})},beforeAvatarUpload(e){const t="image/jpeg"===e.type||"image/png"===e.type,a=e.size/1024/1024<2;return t||this.$message.error("上传头像图片只能是 JPG/PNG 格式!"),a||this.$message.error("上传头像图片大小不能超过 2MB!"),t&&a},uploadAvatar(e){const{file:t}=e;Object(n["a"])(t).then(e=>{this.accountForm.avatar=e.data.url,this.$message.success("头像上传成功")}).catch(e=>{console.error("上传失败:",e),this.$message.error("头像上传失败")})},submitForm(){this.$refs.accountForm.validate(e=>{if(e){const e={...this.accountForm};delete e.confirmPassword,this.isEdit?(delete e.password,Object(s["k"])(this.accountId,e).then(()=>{this.$message.success("更新成功"),this.getRoleOptions(),this.goBack()})):Object(s["a"])(e).then(t=>{this.$message.success("创建成功"),console.log("创建账号成功，返回数据:",t),this.getRoleOptions();try{const a=localStorage.getItem("wifi_admin_accounts"),o=a?JSON.parse(a):[],r={id:t.data&&t.data.id?t.data.id:o.length>0?Math.max(...o.map(e=>e.id))+1:1,username:e.username,nickname:e.real_name||e.username,real_name:e.real_name||"",email:e.email||"",phone:e.phone||"",avatar:e.avatar||"",role_id:parseInt(e.role_id),status:parseInt(e.status)||1,last_login_time:"",last_login_ip:"",created_at:(new Date).toISOString().replace("T"," ").slice(0,19),updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)},s=localStorage.getItem("wifi_admin_roles");if(s){const t=JSON.parse(s),a=t.find(t=>t.id===parseInt(e.role_id));a&&(r.role_name=a.name)}o.push(r),localStorage.setItem("wifi_admin_accounts",JSON.stringify(o)),console.log("更新后的账号列表:",o),localStorage.removeItem("wifi_admin_accounts_processed"),localStorage.removeItem("wifi_admin_accounts_cache")}catch(a){console.error("更新localStorage中的账号数据失败:",a)}this.goBack()})}})},goBack(){this.$router.push("/system/account")},createNewRole(){this.$router.push({path:"/system/role/create",query:{referrer:"account-create"}})}}},c=i,l=(a("fd92"),a("2877")),m=Object(l["a"])(c,o,r,!1,null,"216f338e",null);t["default"]=m.exports},8593:function(e,t,a){"use strict";a.d(t,"j",(function(){return d})),a.d(t,"m",(function(){return u})),a.d(t,"i",(function(){return h})),a.d(t,"b",(function(){return _})),a.d(t,"l",(function(){return w})),a.d(t,"d",(function(){return v})),a.d(t,"e",(function(){return I})),a.d(t,"a",(function(){return b})),a.d(t,"k",(function(){return S})),a.d(t,"c",(function(){return O})),a.d(t,"h",(function(){return M})),a.d(t,"g",(function(){return y})),a.d(t,"f",(function(){return F}));a("d9e2"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732");var o=a("b775");const r=!1,s=[{id:1,name:"超级管理员",code:"super_admin",description:"拥有系统所有权限",permissions:["*"],status:1,created_at:"2024-01-01 00:00:00",updated_at:"2024-01-01 00:00:00"},{id:2,name:"运营管理员",code:"operation_admin",description:"负责日常运营管理",permissions:["wifi:*","user:*","order:*","ad:*"],status:1,created_at:"2024-01-15 10:00:00",updated_at:"2024-12-20 15:00:00"}],n=[{id:1,username:"admin",nickname:"超级管理员",email:"<EMAIL>",phone:"13800138000",avatar:'data:image/svg+xml,%3Csvg width="100" height="100" xmlns="http://www.w3.org/2000/svg"%3E%3Ccircle cx="50" cy="50" r="50" fill="%234A90E2"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="white" font-size="40"%3EA%3C/text%3E%3C/svg%3E',role_id:1,role_name:"超级管理员",status:1,last_login_time:"2025-7-7 14:00:00",last_login_ip:"127.0.0.1",created_at:"2024-01-01 00:00:00",updated_at:"2025-7-7 14:00:00"},{id:2,username:"operation",nickname:"运营小王",email:"<EMAIL>",phone:"***********",avatar:"",role_id:2,role_name:"运营管理员",status:1,last_login_time:"2025-7-7 09:00:00",last_login_ip:"*************",created_at:"2024-01-15 10:00:00",updated_at:"2025-7-7 09:00:00"}];let i=[];try{const e=localStorage.getItem("wifi_admin_accounts");i=e?JSON.parse(e):n,e||localStorage.setItem("wifi_admin_accounts",JSON.stringify(n))}catch(j){console.error("初始化mockAccounts失败:",j),i=n}const c=[{id:1,user_id:1,username:"admin",module:"系统管理",action:"更新系统配置",method:"PUT",url:"/api/v1/admin/system/config/update",ip:"127.0.0.1",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)",params:'{"site_name":"华红WIFI共享商业系统"}',result:"success",created_at:"2025-7-7 15:30:00"},{id:2,user_id:2,username:"operation",module:"WiFi管理",action:"创建WiFi热点",method:"POST",url:"/api/v1/admin/wifi/create",ip:"*************",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)",params:'{"name":"星巴克-朝阳店","password":"starbucks2024"}',result:"success",created_at:"2025-7-7 14:20:00"},{id:3,user_id:3,username:"finance",module:"提现管理",action:"审核提现申请",method:"PUT",url:"/api/v1/admin/withdraw/audit/1",ip:"*************",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)",params:'{"status":1,"remark":"审核通过"}',result:"success",created_at:"2025-7-7 11:00:00"}];function l(e){console.log("保存账号数据到localStorage:",e),localStorage.setItem("wifi_admin_accounts",JSON.stringify(e)),localStorage.removeItem("wifi_admin_accounts_processed"),localStorage.removeItem("wifi_admin_accounts_cache")}function m(){try{localStorage.removeItem("wifi_admin_accounts_processed"),localStorage.removeItem("wifi_admin_accounts_cache");const e=localStorage.getItem("wifi_admin_accounts");if(console.log("getStoredAccounts - 从localStorage获取的原始数据:",e),e){const t=JSON.parse(e);return console.log("getStoredAccounts - 解析后的账号数据:",t),t}}catch(j){console.error("getStoredAccounts - 从localStorage获取账号数据失败:",j)}return console.log("getStoredAccounts - 返回默认账号数据"),n}function d(){return r?new Promise(e=>{setTimeout(()=>{const t=getStoredSystemConfig();e({code:200,data:t,message:"获取成功"})},200)}):Object(o["a"])({url:"/api/v1/admin/system/config",method:"get"})}function u(e){return r?new Promise(t=>{setTimeout(()=>{const a=getStoredSystemConfig();e.basic&&Object.assign(a.basic,e.basic),e.payment&&Object.assign(a.payment,e.payment),e.logistics&&Object.assign(a.logistics,e.logistics),e.sms&&(Object.assign(a.sms,e.sms),e.sms.templates&&Object.assign(a.sms.templates,e.sms.templates)),a.updated_at=(new Date).toISOString().replace("T"," ").slice(0,19),saveSystemConfig(a),t({code:200,message:"更新成功"})},500)}):Object(o["a"])({url:"/api/v1/admin/system/config/update",method:"put",data:e})}function p(){const e=localStorage.getItem("wifi_admin_roles");return e?JSON.parse(e):s}function g(e){localStorage.setItem("wifi_admin_roles",JSON.stringify(e))}let f=p();function h(){return new Promise(e=>{setTimeout(()=>{const t=p();f=t,e({code:200,data:{list:t,total:t.length},message:"获取成功"})},200)})}function _(e){return new Promise(t=>{setTimeout(()=>{const a=p();if(a.some(t=>t.name===e.name))return void t({code:400,message:"角色名称已存在"});if(e.code&&a.some(t=>t.code===e.code))return void t({code:400,message:"角色编码已存在"});const o={...e,id:a.length>0?Math.max(...a.map(e=>e.id))+1:1,created_at:(new Date).toISOString().replace("T"," ").slice(0,19),updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)};a.push(o),g(a),f=a,t({code:200,data:o,message:"创建成功"})},500)})}function w(e,t){return new Promise((a,o)=>{setTimeout(()=>{const r=p(),s=r.findIndex(t=>t.id===parseInt(e));if(s>-1){if(1===e&&(t.code!==r[s].code||0===t.status))return void o(new Error("不能修改超级管理员的关键信息"));r[s]={...r[s],...t,updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)},g(r),f=r,a({code:200,data:r[s],message:"更新成功"})}else o(new Error("角色不存在"))},500)})}function v(e){return new Promise((t,a)=>{setTimeout(()=>{if(1===parseInt(e))return void a(new Error("超级管理员角色不能删除"));const o=p(),r=o.findIndex(t=>t.id===parseInt(e));r>-1?(o.splice(r,1),g(o),f=o,t({code:200,message:"删除成功"})):a(new Error("角色不存在"))},300)})}function I(e){return r?new Promise(t=>{setTimeout(()=>{let a=m();console.log("getAccountList获取到的原始账号数据:",a);const o=p();console.log("获取到的角色数据:",o),a=a.map(e=>{const t=o.find(t=>t.id===e.role_id);return{...e,role_name:t?t.name:"未知角色"}}),e&&(e.username&&(a=a.filter(t=>t.username.includes(e.username))),e.nickname&&(a=a.filter(t=>t.nickname&&t.nickname.includes(e.nickname))),e.role_id&&(a=a.filter(t=>t.role_id===parseInt(e.role_id))),void 0!==e.status&&""!==e.status&&(a=a.filter(t=>t.status===parseInt(e.status)))),localStorage.setItem("wifi_admin_accounts_processed",JSON.stringify(a)),console.log("getAccountList处理后的账号数据:",a),t({code:200,data:{total:a.length,list:a},message:"获取成功"})},200)}):Object(o["a"])({url:"/api/v1/admin/system/account/list",method:"get",params:e})}function b(e){return r?new Promise((t,a)=>{setTimeout(()=>{const o=m();if(o.some(t=>t.username===e.username))return void a({code:400,message:"用户名已存在"});const r=p();console.log("创建账号时获取的角色数据:",r);const s=r.find(t=>t.id===parseInt(e.role_id));if(!s)return void a({code:400,message:"角色不存在"});const n={id:o.length>0?Math.max(...o.map(e=>e.id))+1:1,username:e.username,nickname:e.real_name||e.username,real_name:e.real_name||"",email:e.email||"",phone:e.phone||"",avatar:e.avatar||"",role_id:parseInt(e.role_id),role_name:s.name,status:parseInt(e.status)||1,last_login_time:"",last_login_ip:"",created_at:(new Date).toISOString().replace("T"," ").slice(0,19),updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)};o.push(n),l(o),localStorage.removeItem("wifi_admin_accounts_processed"),localStorage.removeItem("wifi_admin_accounts_cache"),t({code:200,data:n,message:"创建成功"})},500)}):Object(o["a"])({url:"/api/v1/admin/system/account/create",method:"post",data:e})}function S(e,t){return r?new Promise((a,o)=>{setTimeout(()=>{const r=m();console.log("更新账号 - 原始账号列表:",r),console.log("更新账号 - ID:",e,"类型:",typeof e),console.log("更新账号 - 数据:",t);const s=parseInt(e);r.forEach(e=>{console.log("账号ID:",e.id,"类型:",typeof e.id)});const n=r.findIndex(e=>parseInt(e.id)===s);if(console.log("找到的账号索引:",n),-1===n)return console.error("账号不存在, ID:",e),void o({code:404,message:"账号不存在"});if(t.username&&t.username!==r[n].username&&r.some(e=>parseInt(e.id)!==s&&e.username===t.username))return void o({code:400,message:"用户名已存在"});const i=p(),c=i.find(e=>e.id===parseInt(t.role_id));if(!c)return void o({code:400,message:"角色不存在"});const d={...r[n],...t,role_id:parseInt(t.role_id),role_name:c.name,updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)};r[n]=d,console.log("更新后的账号:",d),console.log("更新后的账号列表:",r),l(r),a({code:200,data:d,message:"更新成功"})},500)}):Object(o["a"])({url:"/api/v1/admin/system/account/update/"+e,method:"put",data:t})}function O(e){return r?new Promise((t,a)=>{setTimeout(()=>{console.log("删除账号 - ID:",e,"类型:",typeof e);const o=parseInt(e);if(1===o)return console.error("尝试删除超级管理员账号"),void a(new Error("超级管理员账号不能删除"));const r=m();console.log("删除账号 - 原始账号列表:",r),r.forEach(e=>{console.log("账号ID:",e.id,"类型:",typeof e.id)});const s=r.findIndex(e=>parseInt(e.id)===o);if(console.log("找到的账号索引:",s),s>-1){const e=r[s];console.log("要删除的账号:",e),r.splice(s,1),console.log("删除后的账号列表:",r),l(r),t({code:200,message:"删除成功"})}else console.error("账号不存在, ID:",e),a(new Error("账号不存在"))},300)}):Object(o["a"])({url:"/api/v1/admin/system/account/delete/"+e,method:"delete"})}function M(e){return r?new Promise(t=>{setTimeout(()=>{const a=parseInt(e.page)||1,o=parseInt(e.limit)||10,r=[...c];for(let e=4;e<=20;e++)r.push({id:e,user_id:Math.floor(4*Math.random())+1,username:["admin","operation","finance","service01"][Math.floor(4*Math.random())],module:["系统管理","WiFi管理","用户管理","订单管理"][Math.floor(4*Math.random())],action:["查看列表","创建记录","更新记录","删除记录"][Math.floor(4*Math.random())],method:["GET","POST","PUT","DELETE"][Math.floor(4*Math.random())],url:"/api/v1/admin/xxx",ip:"192.168.1."+(100+Math.floor(10*Math.random())),user_agent:"Mozilla/5.0",params:"{}",result:Math.random()>.1?"success":"error",created_at:new Date(Date.now()-36e5*e).toISOString().replace("T"," ").slice(0,19)});r.sort((e,t)=>new Date(t.created_at)-new Date(e.created_at));const s=r.length,n=(a-1)*o,i=n+o,l=r.slice(n,i);t({code:200,data:{list:l,total:s,page:a,limit:o},message:"获取成功"})},300)}):Object(o["a"])({url:"/api/v1/admin/log/operation",method:"get",params:e})}function y(e){return r?new Promise(t=>{setTimeout(()=>{const a=parseInt(e.page)||1,o=parseInt(e.limit)||10,r=[];for(let e=1;e<=30;e++)r.push({id:e,user_id:Math.floor(4*Math.random())+1,username:["admin","operation","finance","service01"][Math.floor(4*Math.random())],ip:"192.168.1."+(100+Math.floor(20*Math.random())),location:["北京市","上海市","广州市","深圳市"][Math.floor(4*Math.random())],browser:["Chrome","Firefox","Safari","Edge"][Math.floor(4*Math.random())],os:["Windows 10","macOS","Ubuntu","Windows 11"][Math.floor(4*Math.random())],status:Math.random()>.2?1:0,message:Math.random()>.2?"登录成功":"密码错误",created_at:new Date(Date.now()-72e5*e).toISOString().replace("T"," ").slice(0,19)});const s=r.length,n=(a-1)*o,i=n+o,c=r.slice(n,i);t({code:200,data:{list:c,total:s,page:a,limit:o},message:"获取成功"})},300)}):Object(o["a"])({url:"/api/v1/admin/log/login",method:"get",params:e})}function F(e){return r?new Promise(t=>{setTimeout(()=>{const a=parseInt(e.page)||1,o=parseInt(e.limit)||10,r=[];for(let e=1;e<=15;e++){const t=Math.random()>.3,a=["Connection timeout","Invalid parameter","File not found","Permission denied","Database connection failed","Redis connection refused","API rate limit exceeded","Memory allocation failed"],o=a[Math.floor(Math.random()*a.length)],s=t?`Error: ${o}\n    at Object.<anonymous> (/app/src/api/wifi.js:45:15)\n    at Module._compile (internal/modules/cjs/loader.js:1063:30)\n    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)\n    at Module.load (internal/modules/cjs/loader.js:928:32)\n    at Function.Module._load (internal/modules/cjs/loader.js:769:14)\n    at Module.require (internal/modules/cjs/loader.js:952:19)\n    at require (internal/modules/cjs/helpers.js:88:18)\n    at Object.<anonymous> (/app/src/router/index.js:12:18)\n    at Module._compile (internal/modules/cjs/loader.js:1063:30)\n    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)`:null;r.push({id:e,level:["error","warning"][Math.floor(2*Math.random())],module:["API","Database","Redis","File"][Math.floor(4*Math.random())],message:o,stack:s,user_id:Math.floor(4*Math.random())+1,username:["admin","operation","finance","service01"][Math.floor(4*Math.random())],ip:"192.168.1."+(100+Math.floor(20*Math.random())),url:"/api/v1/admin/xxx",created_at:new Date(Date.now()-108e5*e).toISOString().replace("T"," ").slice(0,19)})}const s=r.length,n=(a-1)*o,i=n+o,c=r.slice(n,i);t({code:200,data:{list:c,total:s,page:a,limit:o},message:"获取成功"})},300)}):Object(o["a"])({url:"/api/v1/admin/log/error",method:"get",params:e})}},"91b6":function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));var o=a("b775");const r=!1;function s(e){const t=Date.now(),a=Math.floor(1e4*Math.random()),o=e.split(".").pop();return`${t}_${a}.${o}`}function n(e){if(r)return new Promise(t=>{setTimeout(()=>{const a=new FileReader;a.onload=a=>{const o=a.target.result,r=s(e.name);t({code:200,data:{url:o,filename:r,size:e.size,type:e.type},message:"上传成功"})},a.readAsDataURL(e)},500)});const t=new FormData;return t.append("file",e),Object(o["a"])({url:"/api/v1/admin/upload",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}},a732:function(e,t,a){"use strict";var o=a("23e7"),r=a("c65b"),s=a("2266"),n=a("59ed"),i=a("825a"),c=a("46c4"),l=a("2a62"),m=a("f99f"),d=m("some",TypeError);o({target:"Iterator",proto:!0,real:!0,forced:d},{some:function(e){i(this);try{n(e)}catch(o){l(this,"throw",o)}if(d)return r(d,this,e);var t=c(this),a=0;return s(t,(function(t,o){if(e(t,a++))return o()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},ab43:function(e,t,a){"use strict";var o=a("23e7"),r=a("c65b"),s=a("59ed"),n=a("825a"),i=a("46c4"),c=a("c5cc"),l=a("9bdd"),m=a("2a62"),d=a("2baa"),u=a("f99f"),p=a("c430"),g=!p&&!d("map",(function(){})),f=!p&&!g&&u("map",TypeError),h=p||g||f,_=c((function(){var e=this.iterator,t=n(r(this.next,e)),a=this.done=!!t.done;if(!a)return l(e,this.mapper,[t.value,this.counter++],!0)}));o({target:"Iterator",proto:!0,real:!0,forced:h},{map:function(e){n(this);try{s(e)}catch(t){m(this,"throw",t)}return f?r(f,this,e):new _(i(this),{mapper:e})}})},f665:function(e,t,a){"use strict";var o=a("23e7"),r=a("c65b"),s=a("2266"),n=a("59ed"),i=a("825a"),c=a("46c4"),l=a("2a62"),m=a("f99f"),d=m("find",TypeError);o({target:"Iterator",proto:!0,real:!0,forced:d},{find:function(e){i(this);try{n(e)}catch(o){l(this,"throw",o)}if(d)return r(d,this,e);var t=c(this),a=0;return s(t,(function(t,o){if(e(t,a++))return o(t)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},f9a5:function(e,t,a){},fd92:function(e,t,a){"use strict";a("f9a5")}}]);