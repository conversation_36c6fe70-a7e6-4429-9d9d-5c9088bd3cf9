(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0e80cf1a"],{"13d5":function(e,t,r){"use strict";var a=r("23e7"),i=r("d58f").left,s=r("a640"),o=r("1212"),n=r("9adc"),m=!n&&o>79&&o<83,l=m||!s("reduce");a({target:"Array",proto:!0,forced:l},{reduce:function(e){var t=arguments.length;return i(this,e,t,t>1?arguments[1]:void 0)}})},1957:function(e,t,r){"use strict";r("6517")},6517:function(e,t,r){},8558:function(e,t,r){"use strict";var a=r("cfe9"),i=r("b5db"),s=r("c6b6"),o=function(e){return i.slice(0,e.length)===e};e.exports=function(){return o("Bun/")?"BUN":o("Cloudflare-Workers")?"CLOUDFLARE":o("Deno/")?"DENO":o("Node.js/")?"NODE":a.Bun&&"string"==typeof Bun.version?"BUN":a.Deno&&"object"==typeof Deno.version?"DENO":"process"===s(a.process)?"NODE":a.window&&a.document?"BROWSER":"REST"}()},8935:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-container"},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("分润规则配置")])]),t("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"150px"}},[t("el-divider",{attrs:{"content-position":"left"}},[e._v("WiFi分润规则")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"平台分润比例",prop:"wifi_platform_rate"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,max:100,precision:2,step:1},model:{value:e.form.wifi_platform_rate,callback:function(t){e.$set(e.form,"wifi_platform_rate",t)},expression:"form.wifi_platform_rate"}}),t("span",{staticClass:"unit"},[e._v("%")])],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"团长分润比例",prop:"wifi_leader_rate"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,max:100,precision:2,step:1},model:{value:e.form.wifi_leader_rate,callback:function(t){e.$set(e.form,"wifi_leader_rate",t)},expression:"form.wifi_leader_rate"}}),t("span",{staticClass:"unit"},[e._v("%")])],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"成员分润比例",prop:"wifi_member_rate"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,max:100,precision:2,step:1},model:{value:e.form.wifi_member_rate,callback:function(t){e.$set(e.form,"wifi_member_rate",t)},expression:"form.wifi_member_rate"}}),t("span",{staticClass:"unit"},[e._v("%")])],1)],1)],1),t("div",{staticClass:"rate-info"},[t("span",{staticClass:"info"},[e._v("分润比例总和: "+e._s(e.getWifiTotalRate)+"%")]),100!==e.getWifiTotalRate?t("span",{staticClass:"error"},[e._v(" (注意: 分润比例总和应等于100%) ")]):e._e()]),t("el-divider",{attrs:{"content-position":"left"}},[e._v("商品分润规则")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"平台分润比例",prop:"goods_platform_rate"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,max:100,precision:2,step:1},model:{value:e.form.goods_platform_rate,callback:function(t){e.$set(e.form,"goods_platform_rate",t)},expression:"form.goods_platform_rate"}}),t("span",{staticClass:"unit"},[e._v("%")])],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"团长分润比例",prop:"goods_leader_rate"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,max:100,precision:2,step:1},model:{value:e.form.goods_leader_rate,callback:function(t){e.$set(e.form,"goods_leader_rate",t)},expression:"form.goods_leader_rate"}}),t("span",{staticClass:"unit"},[e._v("%")])],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"成员分润比例",prop:"goods_member_rate"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,max:100,precision:2,step:1},model:{value:e.form.goods_member_rate,callback:function(t){e.$set(e.form,"goods_member_rate",t)},expression:"form.goods_member_rate"}}),t("span",{staticClass:"unit"},[e._v("%")])],1)],1)],1),t("div",{staticClass:"rate-info"},[t("span",{staticClass:"info"},[e._v("分润比例总和: "+e._s(e.getGoodsTotalRate)+"%")]),100!==e.getGoodsTotalRate?t("span",{staticClass:"error"},[e._v(" (注意: 分润比例总和应等于100%) ")]):e._e()]),t("el-divider",{attrs:{"content-position":"left"}},[e._v("广告分润规则")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"平台分润比例",prop:"ad_platform_rate"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,max:100,precision:2,step:1},model:{value:e.form.ad_platform_rate,callback:function(t){e.$set(e.form,"ad_platform_rate",t)},expression:"form.ad_platform_rate"}}),t("span",{staticClass:"unit"},[e._v("%")])],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"团长分润比例",prop:"ad_leader_rate"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,max:100,precision:2,step:1},model:{value:e.form.ad_leader_rate,callback:function(t){e.$set(e.form,"ad_leader_rate",t)},expression:"form.ad_leader_rate"}}),t("span",{staticClass:"unit"},[e._v("%")])],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"成员分润比例",prop:"ad_member_rate"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,max:100,precision:2,step:1},model:{value:e.form.ad_member_rate,callback:function(t){e.$set(e.form,"ad_member_rate",t)},expression:"form.ad_member_rate"}}),t("span",{staticClass:"unit"},[e._v("%")])],1)],1)],1),t("div",{staticClass:"rate-info"},[t("span",{staticClass:"info"},[e._v("分润比例总和: "+e._s(e.getAdTotalRate)+"%")]),100!==e.getAdTotalRate?t("span",{staticClass:"error"},[e._v(" (注意: 分润比例总和应等于100%) ")]):e._e()]),t("el-divider",{attrs:{"content-position":"left"}},[e._v("提现设置")]),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"最小提现金额",prop:"min_withdraw_amount"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,precision:2,step:10},model:{value:e.form.min_withdraw_amount,callback:function(t){e.$set(e.form,"min_withdraw_amount",t)},expression:"form.min_withdraw_amount"}}),t("span",{staticClass:"unit"},[e._v("元")])],1)],1),t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"提现手续费",prop:"withdraw_fee_rate"}},[t("el-input-number",{staticStyle:{width:"180px"},attrs:{min:0,max:100,precision:2,step:.1},model:{value:e.form.withdraw_fee_rate,callback:function(t){e.$set(e.form,"withdraw_fee_rate",t)},expression:"form.withdraw_fee_rate"}}),t("span",{staticClass:"unit"},[e._v("%")])],1)],1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保存配置")]),t("el-button",{on:{click:e.resetForm}},[e._v("重置")])],1)],1)],1)],1)])},i=[],s=r("f851"),o={name:"ProfitRules",data(){return{loading:!1,form:{wifi_platform_rate:40,wifi_leader_rate:40,wifi_member_rate:20,goods_platform_rate:50,goods_leader_rate:30,goods_member_rate:20,ad_platform_rate:60,ad_leader_rate:30,ad_member_rate:10,min_withdraw_amount:50,withdraw_fee_rate:1},rules:{wifi_platform_rate:[{required:!0,message:"请输入平台分润比例",trigger:"blur"}],wifi_leader_rate:[{required:!0,message:"请输入团长分润比例",trigger:"blur"}],wifi_member_rate:[{required:!0,message:"请输入成员分润比例",trigger:"blur"}],goods_platform_rate:[{required:!0,message:"请输入平台分润比例",trigger:"blur"}],goods_leader_rate:[{required:!0,message:"请输入团长分润比例",trigger:"blur"}],goods_member_rate:[{required:!0,message:"请输入成员分润比例",trigger:"blur"}],ad_platform_rate:[{required:!0,message:"请输入平台分润比例",trigger:"blur"}],ad_leader_rate:[{required:!0,message:"请输入团长分润比例",trigger:"blur"}],ad_member_rate:[{required:!0,message:"请输入成员分润比例",trigger:"blur"}],min_withdraw_amount:[{required:!0,message:"请输入最小提现金额",trigger:"blur"}],withdraw_fee_rate:[{required:!0,message:"请输入提现手续费",trigger:"blur"}]}}},computed:{getWifiTotalRate(){return Number(this.form.wifi_platform_rate)+Number(this.form.wifi_leader_rate)+Number(this.form.wifi_member_rate)},getGoodsTotalRate(){return Number(this.form.goods_platform_rate)+Number(this.form.goods_leader_rate)+Number(this.form.goods_member_rate)},getAdTotalRate(){return Number(this.form.ad_platform_rate)+Number(this.form.ad_leader_rate)+Number(this.form.ad_member_rate)}},created(){this.fetchRules()},methods:{fetchRules(){this.loading=!0,Object(s["e"])().then(e=>{const t=e.data;t.wifi_share&&(this.form.wifi_platform_rate=t.wifi_share.platform_rate||40,this.form.wifi_leader_rate=t.wifi_share.leader_rate||40,this.form.wifi_member_rate=t.wifi_share.user_rate||20),t.goods_sale&&(this.form.goods_platform_rate=t.goods_sale.platform_rate||50,this.form.goods_leader_rate=t.goods_sale.leader_rate||30,this.form.goods_member_rate=t.goods_sale.user_rate||20),t.advertisement&&(this.form.ad_platform_rate=t.advertisement.platform_rate||60,this.form.ad_leader_rate=t.advertisement.leader_rate||30,this.form.ad_member_rate=t.advertisement.user_rate||10),this.form.min_withdraw_amount=t.min_withdraw_amount||50,this.form.withdraw_fee_rate=t.withdraw_fee_rate||1,this.loading=!1}).catch(()=>{this.loading=!1})},submitForm(){this.$refs.form.validate(e=>{e&&(100!==this.getWifiTotalRate||100!==this.getGoodsTotalRate||100!==this.getAdTotalRate?this.$confirm("分润比例总和不等于100%，确认要保存吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.saveRules()}).catch(()=>{}):this.saveRules())})},saveRules(){this.loading=!0;const e={wifi_share:{name:"WiFi分享",platform_rate:this.form.wifi_platform_rate,leader_rate:this.form.wifi_leader_rate,user_rate:this.form.wifi_member_rate,status:1},goods_sale:{name:"商品销售",platform_rate:this.form.goods_platform_rate,leader_rate:this.form.goods_leader_rate,user_rate:this.form.goods_member_rate,status:1},advertisement:{name:"广告点击",platform_rate:this.form.ad_platform_rate,leader_rate:this.form.ad_leader_rate,user_rate:this.form.ad_member_rate,status:1},min_withdraw_amount:this.form.min_withdraw_amount,withdraw_fee_rate:this.form.withdraw_fee_rate};Object(s["h"])(e).then(e=>{this.$message.success("保存成功"),this.fetchRules()}).catch(()=>{this.loading=!1,this.$message.error("保存失败")})},resetForm(){this.$refs.form.resetFields(),this.fetchRules()}}},n=o,m=(r("1957"),r("2877")),l=Object(m["a"])(n,a,i,!1,null,"0e8d2384",null);t["default"]=l.exports},9485:function(e,t,r){"use strict";var a=r("23e7"),i=r("2266"),s=r("59ed"),o=r("825a"),n=r("46c4"),m=r("2a62"),l=r("f99f"),_=r("2ba4"),u=r("d039"),d=TypeError,c=u((function(){[].keys().reduce((function(){}),void 0)})),f=!c&&l("reduce",d);a({target:"Iterator",proto:!0,real:!0,forced:c||f},{reduce:function(e){o(this);try{s(e)}catch(u){m(this,"throw",u)}var t=arguments.length<2,r=t?void 0:arguments[1];if(f)return _(f,this,t?[e]:[e,r]);var a=n(this),l=0;if(i(a,(function(a){t?(t=!1,r=a):r=e(r,a,l),l++}),{IS_RECORD:!0}),t)throw new d("Reduce of empty iterator with no initial value");return r}})},"9adc":function(e,t,r){"use strict";var a=r("8558");e.exports="NODE"===a},a640:function(e,t,r){"use strict";var a=r("d039");e.exports=function(e,t){var r=[][e];return!!r&&a((function(){r.call(null,t||function(){return 1},1)}))}},d58f:function(e,t,r){"use strict";var a=r("59ed"),i=r("7b0b"),s=r("44ad"),o=r("07fa"),n=TypeError,m="Reduce of empty array with no initial value",l=function(e){return function(t,r,l,_){var u=i(t),d=s(u),c=o(u);if(a(r),0===c&&l<2)throw new n(m);var f=e?c-1:0,p=e?-1:1;if(l<2)while(1){if(f in d){_=d[f],f+=p;break}if(f+=p,e?f<0:c<=f)throw new n(m)}for(;e?f>=0:c>f;f+=p)f in d&&(_=r(_,d[f],f,u));return _}};e.exports={left:l(!1),right:l(!0)}},f665:function(e,t,r){"use strict";var a=r("23e7"),i=r("c65b"),s=r("2266"),o=r("59ed"),n=r("825a"),m=r("46c4"),l=r("2a62"),_=r("f99f"),u=_("find",TypeError);a({target:"Iterator",proto:!0,real:!0,forced:u},{find:function(e){n(this);try{o(e)}catch(a){l(this,"throw",a)}if(u)return i(u,this,e);var t=m(this),r=0;return s(t,(function(t,a){if(e(t,r++))return a(t)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},f851:function(e,t,r){"use strict";r.d(t,"e",(function(){return h})),r.d(t,"h",(function(){return w})),r.d(t,"d",(function(){return g})),r.d(t,"c",(function(){return b})),r.d(t,"g",(function(){return v})),r.d(t,"f",(function(){return y})),r.d(t,"a",(function(){return x})),r.d(t,"b",(function(){return k}));r("d9e2"),r("13d5"),r("e9f5"),r("910d"),r("f665"),r("9485");var a=r("b775");const i=!1,s="wifi_admin_profit_rules",o="wifi_admin_profit_bills",n="wifi_admin_withdraw_list",m={wifi_share:{name:"WiFi分享",user_rate:70,platform_rate:30,status:1},goods_sale:{name:"商品销售",user_rate:10,leader_rate:5,platform_rate:85,status:1},advertisement:{name:"广告点击",user_rate:20,leader_rate:10,platform_rate:70,status:1},updated_at:"2025-07-08 16:30:00"};function l(){try{const e=localStorage.getItem(s);return e?JSON.parse(e):m}catch(e){return console.warn("读取分润规则数据失败，使用默认数据:",e),m}}function _(e){try{localStorage.setItem(s,JSON.stringify(e))}catch(t){console.error("保存分润规则数据失败:",t)}}const u=[{id:101,bill_no:"SB20250708001",type:"wifi_share",amount:.6,share_amount:.42,platform_amount:.18,user_id:1001,user_name:"张三",user_phone:"138****1001",source_id:1,source_type:"wifi",status:1,remark:"WiFi码分享使用收益",settle_time:null,created_at:"2025-07-08 10:15:22"},{id:102,bill_no:"SB20250708002",type:"wifi_share",amount:.4,share_amount:.28,platform_amount:.12,user_id:1002,user_name:"李四",user_phone:"138****1002",source_id:2,source_type:"wifi",status:2,remark:"WiFi码分享使用收益",settle_time:"2025-07-08 16:32:45",created_at:"2025-07-08 09:27:18"},{id:103,bill_no:"SB20250708003",type:"goods_sale",amount:25,share_amount:2.5,platform_amount:22.5,user_id:1003,user_name:"王五",user_phone:"138****1003",source_id:1,source_type:"order",status:1,remark:"商品销售分润",settle_time:null,created_at:"2025-07-08 14:52:36"},{id:104,bill_no:"SB20250708004",type:"advertisement",amount:5.6,share_amount:1.12,platform_amount:4.48,user_id:1001,user_name:"张三",user_phone:"138****1001",source_id:3,source_type:"ad_click",status:2,remark:"广告点击收益",settle_time:"2025-07-08 18:21:05",created_at:"2025-07-08 16:05:43"}];function d(){try{const e=localStorage.getItem(o);return e?JSON.parse(e):u}catch(e){return console.warn("读取账单数据失败，使用默认数据:",e),u}}const c=[{id:1,withdraw_no:"W2025070800001",user_id:1001,user_name:"张三",user_phone:"138****1001",amount:200,status:0,type:1,account_type:"支付宝",account_name:"张三",account_no:"<EMAIL>",bank_name:null,created_at:"2025-07-08 10:12:33",audit_time:null,audit_user:null,audit_remark:null,pay_time:null,pay_remark:null},{id:2,withdraw_no:"W2025070800002",user_id:1002,user_name:"李四",user_phone:"138****1002",amount:500,status:1,type:2,account_type:"银行卡",account_name:"李四",account_no:"6222xxxxxxx",bank_name:"工商银行",created_at:"2025-07-08 09:45:21",audit_time:"2025-07-08 11:23:45",audit_user:"admin",audit_remark:"审核通过",pay_time:null,pay_remark:null},{id:3,withdraw_no:"W2025070800003",user_id:1003,user_name:"王五",user_phone:"138****1003",amount:100,status:2,type:1,account_type:"支付宝",account_name:"王五",account_no:"<EMAIL>",bank_name:null,created_at:"2025-07-07 16:32:18",audit_time:"2025-07-08 09:15:30",audit_user:"admin",audit_remark:"金额不足，请重新提交",pay_time:null,pay_remark:null},{id:4,withdraw_no:"W2025070800004",user_id:1004,user_name:"赵六",user_phone:"138****1004",amount:300,status:3,type:2,account_type:"银行卡",account_name:"赵六",account_no:"6217xxxxxxx",bank_name:"招商银行",created_at:"2025-07-07 14:56:42",audit_time:"2025-07-07 16:28:35",audit_user:"admin",audit_remark:"审核通过",pay_time:"2025-07-08 10:35:12",pay_remark:"打款成功"}];function f(){try{const e=localStorage.getItem(n);return e?JSON.parse(e):c}catch(e){return console.warn("读取提现数据失败，使用默认数据:",e),c}}function p(e){try{localStorage.setItem(n,JSON.stringify(e))}catch(t){console.error("保存提现数据失败:",t)}}function h(){return i?new Promise(e=>{setTimeout(()=>{const t=l();e({code:200,data:t,message:"获取成功"})},200)}):Object(a["a"])({url:"/api/v1/admin/income/rules",method:"get"})}function w(e){return i?new Promise(t=>{setTimeout(()=>{const r=l(),a=Object.assign(r,e,{updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)});_(a),t({code:200,message:"更新成功"})},500)}):Object(a["a"])({url:"/api/v1/admin/income/rules/update",method:"post",data:e})}function g(e){return i?new Promise(t=>{setTimeout(()=>{const r=d(),a=parseInt(e.page)||1,i=parseInt(e.limit)||10;let s=[...r];e.keyword&&(s=s.filter(t=>t.user_name.includes(e.keyword)||t.user_phone.includes(e.keyword)||t.bill_no.includes(e.keyword))),e.type&&(s=s.filter(t=>t.type===e.type)),void 0!==e.status&&""!==e.status&&(s=s.filter(t=>t.status===parseInt(e.status))),e.start_date&&e.end_date&&(s=s.filter(t=>{const r=new Date(t.created_at),a=new Date(e.start_date),i=new Date(e.end_date);return i.setHours(23,59,59,999),r>=a&&r<=i}));const o=s.length,n=(a-1)*i,m=n+i,l=s.slice(n,m),_=s.reduce((e,t)=>e+t.amount,0),u=s.reduce((e,t)=>e+t.share_amount,0),c=s.reduce((e,t)=>e+t.platform_amount,0);t({code:200,data:{list:l,total:o,page:a,limit:i,stats:{total_amount:_.toFixed(2),total_share_amount:u.toFixed(2),total_platform_amount:c.toFixed(2)}},message:"获取成功"})},300)}):Object(a["a"])({url:"/api/v1/admin/income/bill/list",method:"get",params:e})}function b(e){return i?new Promise((t,r)=>{setTimeout(()=>{try{const a=d(),i=a.find(t=>t.id===parseInt(e));if(!i)return void r(new Error("账单不存在"));const s={code:200,data:{detail:{id:i.id,amount:i.amount,source_type:"wifi_share"===i.type?1:"goods_sale"===i.type?2:3,source_id:i.bill_no,created_at:i.created_at,remark:i.remark},user_info:{id:i.user_id,nickname:i.user_name,phone:i.user_phone,avatar:"",is_leader:Math.random()>.5?1:0,balance:(1e3*Math.random()).toFixed(2)},source_info:"wifi_share"===i.type?{title:"WiFi示例",name:"Test_WiFi_"+i.id,merchant_name:"示例商户",use_count:Math.floor(100*Math.random())}:"goods_sale"===i.type?{order_no:i.bill_no,total_amount:i.amount,status:1,created_at:i.created_at}:{title:"广告示例",space_name:"首页广告位",click_count:Math.floor(1e3*Math.random()),view_count:Math.floor(5e3*Math.random())},profit_detail:[{role:"分享者",rate:70,amount:i.share_amount,user_info:`${i.user_name}(${i.user_phone})`},{role:"平台",rate:30,amount:i.platform_amount,user_info:"系统平台"}]},message:"success"};t(s)}catch(a){r(a)}},200)}):Object(a["a"])({url:"/api/v1/admin/income/bill/detail/"+e,method:"get"})}function v(e){return i?new Promise(t=>{setTimeout(()=>{const r=f(),a=parseInt(e.page)||1,i=parseInt(e.limit)||10;let s=[...r];e.keyword&&(s=s.filter(t=>t.user_name.includes(e.keyword)||t.user_phone.includes(e.keyword)||t.withdraw_no.includes(e.keyword))),void 0!==e.status&&""!==e.status&&(s=s.filter(t=>t.status===parseInt(e.status))),e.start_date&&e.end_date&&(s=s.filter(t=>{const r=new Date(t.created_at),a=new Date(e.start_date),i=new Date(e.end_date);return i.setHours(23,59,59,999),r>=a&&r<=i}));const o=s.length,n=(a-1)*i,m=n+i,l=s.slice(n,m);t({code:200,data:{list:l,total:o,page:a,limit:i},message:"获取成功"})},300)}):Object(a["a"])({url:"/api/v1/admin/withdraw/list",method:"get",params:e})}function y(e){return i?new Promise((t,r)=>{setTimeout(()=>{const a=f(),i=a.find(t=>t.id===parseInt(e));if(i){const e={detail:{...i,card_holder:i.account_name,bank_name:i.bank_name||"支付宝",card_number:i.account_no,bank_branch:"工商银行"===i.bank_name?"北京市朝阳区支行":null,transfer_time:i.pay_time,transaction_id:3===i.status?"TRX202412270001":null,remark:i.audit_remark||i.pay_remark||null},user_info:{id:i.user_id,nickname:i.user_name,phone:i.user_phone,avatar:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",balance:1580.5,is_leader:101===i.user_id?1:0}};t({code:200,data:e,message:"获取成功"})}else r(new Error("提现申请不存在"))},200)}):Object(a["a"])({url:"/api/v1/admin/withdraw/detail/"+e,method:"get"})}function x(e,t){return i?new Promise((r,a)=>{setTimeout(()=>{const i=f(),s=i.findIndex(t=>t.id===parseInt(e));s>-1?(i[s].status=t.status,i[s].audit_time=(new Date).toISOString().replace("T"," ").slice(0,19),i[s].audit_user="admin",i[s].audit_remark=t.remark||(1===t.status?"审核通过":"审核拒绝"),p(i),r({code:200,message:"审核成功"})):a(new Error("提现申请不存在"))},500)}):Object(a["a"])({url:"/api/v1/admin/withdraw/audit/"+e,method:"post",data:t})}function k(e,t){return i?new Promise((r,a)=>{setTimeout(()=>{const i=f(),s=i.findIndex(t=>t.id===parseInt(e));s>-1?(i[s].status=3,i[s].pay_time=(new Date).toISOString().replace("T"," ").slice(0,19),i[s].pay_remark=t.remark||"已打款",i[s].transaction_id=t.transaction_id,p(i),r({code:200,message:"打款成功"})):a(new Error("提现申请不存在"))},500)}):Object(a["a"])({url:"/api/v1/admin/withdraw/confirm/"+e,method:"post",data:t})}}}]);