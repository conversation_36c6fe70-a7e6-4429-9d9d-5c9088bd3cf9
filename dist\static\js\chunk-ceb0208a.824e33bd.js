(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ceb0208a"],{"330e":function(t,e,a){"use strict";a("34b0")},"333d":function(t,e,a){"use strict";var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[e("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},s=[],l={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get(){return this.page},set(t){this.$emit("update:page",t)}},pageSize:{get(){return this.limit},set(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange(t){this.$emit("pagination",{page:this.currentPage,limit:t})},handleCurrentChange(t){this.$emit("pagination",{page:t,limit:this.pageSize})}}},n=l,r=(a("330e"),a("2877")),o=Object(r["a"])(n,i,s,!1,null,"11252b03",null);e["a"]=o.exports},"34b0":function(t,e,a){},6724:function(t,e,a){"use strict";a("8d41");const i={bind(t,e){t.addEventListener("click",a=>{const i=Object.assign({},e.value),s=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),l=s.ele;if(l){l.style.position="relative",l.style.overflow="hidden";const t=l.getBoundingClientRect();let e=l.querySelector(".waves-ripple");switch(e?e.className="waves-ripple":(e=document.createElement("span"),e.className="waves-ripple",e.style.height=e.style.width=Math.max(t.width,t.height)+"px",l.appendChild(e)),s.type){case"center":e.style.top=t.height/2-e.offsetHeight/2+"px",e.style.left=t.width/2-e.offsetWidth/2+"px";break;default:e.style.top=(a.pageY-t.top-e.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",e.style.left=(a.pageX-t.left-e.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return e.style.backgroundColor=s.color,e.className="waves-ripple z-active",!1}},!1)}};var s=i;const l=function(t){t.directive("waves",s)};window.Vue&&(window.waves=s,Vue.use(l)),s.install=l;e["a"]=s},"8d41":function(t,e,a){},"8eb6":function(t,e,a){},c6cd6:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{staticClass:"filter-container"},[e("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{placeholder:"申请状态",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.statusOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),e("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleFilter}},[t._v(" 搜索 ")])],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":""}},[e("el-table-column",{attrs:{label:"ID",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.id)+" ")]}}])}),e("el-table-column",{attrs:{label:"用户信息",width:"180"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"user-info"},[e("el-avatar",{attrs:{size:40,src:a.row.avatar||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"}}),e("div",{staticClass:"user-detail"},[e("div",[t._v(t._s(a.row.nickname||"未设置昵称"))]),e("div",{staticClass:"user-phone"},[t._v(t._s(a.row.user_phone||"未绑定手机"))])])],1)]}}])}),e("el-table-column",{attrs:{label:"团队名称",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.name)+" ")]}}])}),e("el-table-column",{attrs:{label:"联系电话",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.phone)+" ")]}}])}),e("el-table-column",{attrs:{label:"区域",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.area)+" ")]}}])}),e("el-table-column",{attrs:{label:"申请时间",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t._f("parseTime")(e.row.created_at,"{y}-{m}-{d} {h}:{i}"))+" ")]}}])}),e("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t._f("statusFilter")(a.row.status)}},[t._v(" "+t._s(t._f("statusTextFilter")(a.row.status))+" ")])]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"240","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.handleDetail(a.row)}}},[t._v(" 详情 ")]),0===a.row.status?e("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return t.handleApprove(a.row)}}},[t._v(" 通过 ")]):t._e(),0===a.row.status?e("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(e){return t.handleReject(a.row)}}},[t._v(" 拒绝 ")]):t._e(),2===a.row.status?e("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(e){return t.handleDelete(a.row)}}},[t._v(" 删除 ")]):t._e()]}}])})],1),e("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}),e("el-dialog",{attrs:{title:t.auditDialogTitle,visible:t.auditDialogVisible,width:"30%"},on:{"update:visible":function(e){t.auditDialogVisible=e}}},[e("el-form",{attrs:{model:t.auditForm,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"备注"}},[e("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入审核备注"},model:{value:t.auditForm.remark,callback:function(e){t.$set(t.auditForm,"remark",e)},expression:"auditForm.remark"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.auditDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.submitAudit}},[t._v("确 定")])],1)],1)],1)},s=[],l=(a("14d9"),a("e61d")),n=a("6724"),r=a("333d"),o={name:"AllianceList",components:{Pagination:r["a"]},directives:{waves:n["a"]},filters:{statusFilter(t){const e={0:"info",1:"success",2:"danger"};return e[t]},statusTextFilter(t){const e={0:"待审核",1:"已通过",2:"已拒绝"};return e[t]}},data(){return{list:null,total:0,listLoading:!0,listQuery:{page:1,limit:10,status:void 0},statusOptions:[{label:"待审核",value:0},{label:"已通过",value:1},{label:"已拒绝",value:2}],auditDialogVisible:!1,auditDialogTitle:"审核",auditForm:{id:null,status:null,remark:""}}},created(){this.getList()},methods:{getList(){this.listLoading=!0,Object(l["d"])(this.listQuery).then(t=>{this.list=t.data.list||[],this.total=t.data.pagination&&t.data.pagination.total||t.data.total||0,this.listLoading=!1}).catch(t=>{console.error("获取联盟申请列表失败:",t),this.list=[],this.total=0,this.listLoading=!1,this.$message.error("获取联盟申请列表失败")})},handleFilter(){this.listQuery.page=1,this.getList()},handleDetail(t){this.$router.push({path:"/user/alliance/detail/"+t.id})},handleApprove(t){this.auditDialogTitle="通过申请",this.auditForm={id:t.id,status:1,remark:""},this.auditDialogVisible=!0},handleReject(t){this.auditDialogTitle="拒绝申请",this.auditForm={id:t.id,status:2,remark:""},this.auditDialogVisible=!0},submitAudit(){Object(l["a"])(this.auditForm.id,{status:this.auditForm.status,remark:this.auditForm.remark}).then(t=>{this.$message({type:"success",message:"审核成功!"}),this.auditDialogVisible=!1,this.getList()})},handleDelete(t){this.$confirm("确定要删除该申请吗？删除后不可恢复！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(l["b"])(t.id).then(t=>{this.$message({type:"success",message:"删除成功!"}),this.getList()})}).catch(()=>{this.$message({type:"info",message:"已取消删除"})})}}},u=o,c=(a("e25d"),a("2877")),d=Object(c["a"])(u,i,s,!1,null,"3358983c",null);e["default"]=d.exports},e25d:function(t,e,a){"use strict";a("8eb6")},e61d:function(t,e,a){"use strict";a.d(e,"d",(function(){return s})),a.d(e,"c",(function(){return l})),a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var i=a("b775");function s(t){return Object(i["a"])({url:"/api/v1/admin/alliance/list",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/api/v1/admin/alliance/detail/"+t,method:"get"})}function n(t,e){return Object(i["a"])({url:"/api/v1/admin/alliance/audit/"+t,method:"post",data:e})}function r(t){return Object(i["a"])({url:"/api/v1/admin/alliance/delete/"+t,method:"delete"}).catch(e=>{if(e.response&&404===e.response.status)return Object(i["a"])({url:"/api/v1/admin/alliance/delete/"+t,method:"post"});throw e})}}}]);