(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1df60e83"],{"167b":function(e,t,a){},"2b3f":function(e,t,a){"use strict";a.d(t,"e",(function(){return r})),a.d(t,"h",(function(){return i})),a.d(t,"b",(function(){return o})),a.d(t,"k",(function(){return n})),a.d(t,"d",(function(){return c})),a.d(t,"l",(function(){return d})),a.d(t,"g",(function(){return l})),a.d(t,"a",(function(){return u})),a.d(t,"i",(function(){return m})),a.d(t,"c",(function(){return p})),a.d(t,"j",(function(){return g})),a.d(t,"f",(function(){return h}));var s=a("b775");function r(e){return e?e.startsWith("http://")||e.startsWith("https://")||e.startsWith("/")?e:"/uploads/"+e:"/uploads/default-ad.jpg"}function i(e){return Object(s["a"])({url:"/api/v1/admin/advertisement/spaces",method:"get",params:e})}function o(e){return Object(s["a"])({url:"/api/v1/admin/advertisement/spaces",method:"post",data:e})}function n(e,t){return Object(s["a"])({url:"/api/v1/admin/advertisement/spaces/"+e,method:"put",data:t})}function c(e){return Object(s["a"])({url:"/api/v1/admin/advertisement/spaces/"+e,method:"delete"})}function d(e,t){return Object(s["a"])({url:"/api/v1/admin/advertisement/spaces/"+e,method:"put",data:{status:t}})}function l(e){return Object(s["a"])({url:"/api/v1/admin/advertisement/contents",method:"get",params:e})}function u(e){return Object(s["a"])({url:"/api/v1/admin/advertisement/contents",method:"post",data:e})}function m(e,t){return Object(s["a"])({url:"/api/v1/admin/advertisement/contents/"+e,method:"put",data:t})}function p(e){return Object(s["a"])({url:"/api/v1/admin/advertisement/contents/"+e,method:"delete"})}function g(e,t){const a=t&&void 0!==t.status?t.status:0;return Object(s["a"])({url:"/api/v1/admin/advertisement/contents/"+e,method:"put",data:{status:a}})}function h(e){return Object(s["a"])({url:"/api/v1/admin/advertisement/contents/"+e,method:"get"})}},"372b":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-container"},[t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(e.isEdit?"编辑广告":"创建广告"))])]),t("el-form",{ref:"adForm",attrs:{model:e.adForm,rules:e.rules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"广告标题",prop:"title"}},[t("el-input",{attrs:{placeholder:"请输入广告标题"},model:{value:e.adForm.title,callback:function(t){e.$set(e.adForm,"title",t)},expression:"adForm.title"}})],1),t("el-form-item",{attrs:{label:"广告位",prop:"space_id"}},[t("el-select",{attrs:{placeholder:"请选择广告位"},model:{value:e.adForm.space_id,callback:function(t){e.$set(e.adForm,"space_id",t)},expression:"adForm.space_id"}},e._l(e.spaceOptions,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"广告图片",prop:"image"}},[t("el-upload",{staticClass:"image-uploader",attrs:{action:"#","show-file-list":!1,"http-request":e.uploadImage,"before-upload":e.beforeImageUpload}},[e.adForm.image||e.imagePreview?t("img",{staticClass:"uploaded-image",attrs:{src:e.adForm.image?e.formatImageUrl(e.adForm.image):e.imagePreview}}):t("i",{staticClass:"el-icon-plus image-uploader-icon"})]),t("div",{staticClass:"tip"},[e._v("图片格式: JPG/PNG/GIF, 大小不超过2MB")])],1),t("el-form-item",{attrs:{label:"链接地址",prop:"url"}},[t("el-input",{attrs:{placeholder:"请输入链接地址"},model:{value:e.adForm.url,callback:function(t){e.$set(e.adForm,"url",t)},expression:"adForm.url"}})],1),t("el-form-item",{attrs:{label:"投放时间"}},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"上线","inactive-text":"下线"},model:{value:e.adForm.status,callback:function(t){e.$set(e.adForm,"status",t)},expression:"adForm.status"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保存")]),t("el-button",{on:{click:e.goBack}},[e._v("返回")])],1)],1)],1)],1)},r=[],i=(a("14d9"),a("2b3f")),o=a("91b6"),n={name:"AdContentForm",data(){return{isEdit:!1,adId:void 0,dateRange:[],adForm:{title:"",space_id:"",image:"",url:"",start_time:"",end_time:"",status:1,view_count:0,click_count:0},rules:{title:[{required:!0,message:"请输入广告标题",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],space_id:[{required:!0,message:"请选择广告位",trigger:"change"}],image:[{required:!0,message:"请上传广告图片",trigger:"change"}],url:[{required:!0,message:"请输入链接地址",trigger:"blur"},{pattern:/^(https?:\/\/)/,message:"请输入有效的URL地址",trigger:"blur"}]},spaceOptions:[],imagePreview:""}},created(){this.getSpaceOptions();const e=this.$route.params&&this.$route.params.id;e&&(this.isEdit=!0,this.adId=parseInt(e),this.getDetail(this.adId))},methods:{formatImageUrl:i["e"],getDetail(e){Object(i["f"])(e).then(e=>{console.log("广告内容详情响应:",e),"success"===e.status?(this.adForm=e.data,void 0===this.adForm.is_recommend&&(this.adForm.is_recommend=0),this.dateRange=[this.adForm.start_time,this.adForm.end_time]):(this.$message.error(e.message||"获取广告详情失败"),this.goBack())}).catch(e=>{console.error("获取广告详情错误:",e),this.$message.error("获取广告详情失败"),this.goBack()})},getSpaceOptions(){Object(i["h"])({page:1,limit:100}).then(e=>{console.log("广告位列表响应:",e),"success"===e.status?this.spaceOptions=e.data.list||[]:this.$message.error(e.message||"获取广告位列表失败")}).catch(e=>{console.error("获取广告位列表错误:",e),this.$message.error("获取广告位列表失败")})},beforeImageUpload(e){const t=e.type.startsWith("image/"),a=e.size/1024/1024<2;if(!t)return this.$message.error("只能上传图片文件!"),!1;if(!a)return this.$message.error("图片大小不能超过2MB!"),!1;const s=new FileReader;return s.readAsDataURL(e),s.onload=()=>{this.imagePreview=s.result},!0},uploadImage(e){const{file:t}=e;Object(o["a"])(t).then(e=>{if(console.log("广告图片上传响应:",e),200===e.code&&e.data&&e.data.url){const t=e.data.url;this.adForm.image=t.includes("/uploads/")?t.substring(t.indexOf("/uploads/")):t,console.log("广告图片上传成功，URL:",this.adForm.image),this.$message.success("图片上传成功")}else this.$message.error("图片上传失败: "+(e.message||"未知错误"))}).catch(e=>{console.error("图片上传失败:",e),this.$message.error("图片上传失败: "+(e.message||"未知错误"))})},submitForm(){this.$refs.adForm.validate(e=>{if(e){if(this.dateRange&&2===this.dateRange.length){const e=e=>{if("string"===typeof e)return e.includes("T")?e.replace("T"," ").replace(/\.\d+Z$/,""):e;{const t=new Date(e);return t.toISOString().slice(0,19).replace("T"," ")}};this.adForm.start_time=e(this.dateRange[0]),this.adForm.end_time=e(this.dateRange[1])}const e={...this.adForm};console.log("提交的数据:",e),this.isEdit?Object(i["i"])(this.adId,e).then(e=>{console.log("更新广告内容响应:",e),"success"===e.status?(this.$message.success("更新成功"),this.goBack()):this.$message.error(e.message||"更新失败")}).catch(e=>{console.error("更新广告内容错误:",e),this.$message.error("更新失败")}):Object(i["a"])(e).then(e=>{console.log("创建广告内容响应:",e),"success"===e.status?(this.$message.success("创建成功"),this.goBack()):this.$message.error(e.message||"创建失败")}).catch(e=>{console.error("创建广告内容错误:",e),this.$message.error("创建失败")})}})},goBack(){this.$router.push("/ad/content")}}},c=n,d=(a("e949"),a("2877")),l=Object(d["a"])(c,s,r,!1,null,"2117eb32",null);t["default"]=l.exports},"91b6":function(e,t,a){"use strict";a.d(t,"a",(function(){return o}));var s=a("b775");const r=!1;function i(e){const t=Date.now(),a=Math.floor(1e4*Math.random()),s=e.split(".").pop();return`${t}_${a}.${s}`}function o(e){if(r)return new Promise(t=>{setTimeout(()=>{const a=new FileReader;a.onload=a=>{const s=a.target.result,r=i(e.name);t({code:200,data:{url:s,filename:r,size:e.size,type:e.type},message:"上传成功"})},a.readAsDataURL(e)},500)});const t=new FormData;return t.append("file",e),Object(s["a"])({url:"/api/v1/admin/upload",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}},e949:function(e,t,a){"use strict";a("167b")}}]);