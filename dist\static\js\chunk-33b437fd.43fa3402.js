(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-33b437fd"],{"226d":function(t,s,e){"use strict";e.r(s);var a=function(){var t=this,s=t._self._c;return s("div",{staticClass:"app-container"},[s("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"order-detail"},[s("el-card",{staticClass:"box-card"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("订单信息")]),s("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.goBack}},[t._v("返回")])],1),s("el-descriptions",{attrs:{column:2,border:""}},[s("el-descriptions-item",{attrs:{label:"订单号"}},[t._v(t._s(t.orderInfo.order_no))]),s("el-descriptions-item",{attrs:{label:"下单时间"}},[t._v(t._s(t.orderInfo.created_at))]),s("el-descriptions-item",{attrs:{label:"订单状态"}},[s("el-tag",{attrs:{type:t.getOrderStatusType(t.orderInfo.status)}},[t._v(t._s(t.getOrderStatusText(t.orderInfo.status)))])],1),s("el-descriptions-item",{attrs:{label:"支付方式"}},[1===t.orderInfo.payment_method?s("el-tag",{attrs:{type:"primary"}},[t._v("微信支付")]):2===t.orderInfo.payment_method?s("el-tag",{attrs:{type:"success"}},[t._v("余额支付")]):s("el-tag",{attrs:{type:"info"}},[t._v("其他")])],1),s("el-descriptions-item",{attrs:{label:"支付时间"}},[t._v(t._s(t.orderInfo.payment_time||"-"))]),s("el-descriptions-item",{attrs:{label:"发货时间"}},[t._v(t._s(t.orderInfo.shipping_time||"-"))]),s("el-descriptions-item",{attrs:{label:"完成时间"}},[t._v(t._s(t.orderInfo.completion_time||"-"))]),s("el-descriptions-item",{attrs:{label:"备注"}},[t._v(t._s(t.orderInfo.remark||"-"))])],1)],1),s("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[s("el-col",{attrs:{span:12}},[s("el-card",{staticClass:"box-card"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("用户信息")])]),s("div",{staticClass:"user-info"},[s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("用户ID：")]),s("span",{staticClass:"value"},[t._v(t._s(t.orderInfo.user_id))])]),s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("用户昵称：")]),s("span",{staticClass:"value"},[t._v(t._s(t.userInfo.nickname||"-"))])]),s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("手机号：")]),s("span",{staticClass:"value"},[t._v(t._s(t.userInfo.phone||"-"))])]),s("div",{staticClass:"info-item"},[s("el-button",{attrs:{type:"text"},on:{click:t.viewUser}},[t._v("查看用户详情")])],1)])])],1),s("el-col",{attrs:{span:12}},[s("el-card",{staticClass:"box-card"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("收货信息")])]),s("div",{staticClass:"address-info"},[s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("收货人：")]),s("span",{staticClass:"value"},[t._v(t._s(t.orderInfo.receiver_name))])]),s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("联系电话：")]),s("span",{staticClass:"value"},[t._v(t._s(t.orderInfo.receiver_phone))])]),s("div",{staticClass:"info-item"},[s("span",{staticClass:"label"},[t._v("收货地址：")]),s("span",{staticClass:"value"},[t._v(t._s(t.orderInfo.receiver_address))])]),t.hasLogistics?s("div",{staticClass:"info-item logistics"},[s("span",{staticClass:"label"},[t._v("物流信息：")]),s("div",{staticClass:"value"},[s("p",[t._v("物流公司："+t._s(t.logisticsInfo.company_name))]),s("p",[t._v("物流单号："+t._s(t.logisticsInfo.logistics_no))])])]):t._e()])])],1)],1),s("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("商品信息")])]),s("el-table",{staticStyle:{width:"100%"},attrs:{data:t.goodsList,border:""}},[s("el-table-column",{attrs:{label:"商品图片",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function({row:t}){return[s("el-image",{staticStyle:{width:"60px",height:"60px"},attrs:{src:t.goods_cover,fit:"cover"}},[s("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[s("i",{staticClass:"el-icon-picture-outline"})])])]}}])}),s("el-table-column",{attrs:{prop:"goods_title",label:"商品名称","min-width":"200"}}),s("el-table-column",{attrs:{prop:"specs_name",label:"规格",width:"100",align:"center"}}),s("el-table-column",{attrs:{prop:"price",label:"单价",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function({row:e}){return[s("span",[t._v(t._s(e.price)+" 元")])]}}])}),s("el-table-column",{attrs:{prop:"quantity",label:"数量",width:"100",align:"center"}}),s("el-table-column",{attrs:{label:"小计",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function({row:e}){return[s("span",[t._v(t._s((e.price*e.quantity).toFixed(2))+" 元")])]}}])})],1),s("div",{staticClass:"order-amount"},[s("div",{staticClass:"amount-item"},[s("span",{staticClass:"amount-label"},[t._v("商品总金额：")]),s("span",{staticClass:"amount-value"},[t._v("¥ "+t._s(t.orderInfo.goods_amount||0))])]),s("div",{staticClass:"amount-item"},[s("span",{staticClass:"amount-label"},[t._v("运费：")]),s("span",{staticClass:"amount-value"},[t._v("¥ "+t._s(t.orderInfo.shipping_fee||0))])]),s("div",{staticClass:"amount-item"},[s("span",{staticClass:"amount-label"},[t._v("优惠金额：")]),s("span",{staticClass:"amount-value discount"},[t._v("- ¥ "+t._s(t.orderInfo.discount_amount||0))])]),s("div",{staticClass:"amount-item total"},[s("span",{staticClass:"amount-label"},[t._v("订单总金额：")]),s("span",{staticClass:"amount-value total"},[t._v("¥ "+t._s(t.orderInfo.total_amount||0))])])])],1),s("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("操作")])]),s("div",{staticClass:"action-buttons"},[1===t.orderInfo.status?s("el-button",{attrs:{type:"success"},on:{click:t.handleShip}},[t._v("发货")]):t._e(),0===t.orderInfo.status?s("el-button",{attrs:{type:"danger"},on:{click:t.handleCancel}},[t._v("取消订单")]):t._e(),2===t.orderInfo.status?s("el-button",{attrs:{type:"primary"},on:{click:t.handleComplete}},[t._v("完成订单")]):t._e(),s("el-button",{attrs:{type:"info"},on:{click:t.printOrder}},[t._v("打印订单")])],1)])],1),s("el-dialog",{attrs:{title:"订单发货",visible:t.shipDialogVisible,width:"500px"},on:{"update:visible":function(s){t.shipDialogVisible=s}}},[s("el-form",{ref:"shipForm",attrs:{model:t.shipForm,rules:t.shipRules,"label-width":"100px"}},[s("el-form-item",{attrs:{label:"物流公司",prop:"logistics_company"}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择物流公司"},model:{value:t.shipForm.logistics_company,callback:function(s){t.$set(t.shipForm,"logistics_company",s)},expression:"shipForm.logistics_company"}},t._l(t.logisticsOptions,(function(t){return s("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),s("el-form-item",{attrs:{label:"物流单号",prop:"logistics_no"}},[s("el-input",{attrs:{placeholder:"请输入物流单号"},model:{value:t.shipForm.logistics_no,callback:function(s){t.$set(t.shipForm,"logistics_no",s)},expression:"shipForm.logistics_no"}})],1)],1),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(s){t.shipDialogVisible=!1}}},[t._v("取消")]),s("el-button",{attrs:{type:"primary"},on:{click:t.confirmShip}},[t._v("确定")])],1)],1)],1)},i=[],l=(e("14d9"),e("f8b7")),o={name:"OrderDetail",data(){return{loading:!0,orderId:null,orderInfo:{},userInfo:{},goodsList:[],logisticsInfo:{},hasLogistics:!1,shipDialogVisible:!1,shipForm:{logistics_company:"",logistics_no:""},shipRules:{logistics_company:[{required:!0,message:"请选择物流公司",trigger:"change"}],logistics_no:[{required:!0,message:"请输入物流单号",trigger:"blur"},{min:5,message:"物流单号长度不能少于5个字符",trigger:"blur"}]},logisticsOptions:[{label:"顺丰速运",value:"SF"},{label:"中通快递",value:"ZTO"},{label:"圆通速递",value:"YTO"},{label:"申通快递",value:"STO"},{label:"韵达快递",value:"YD"},{label:"天天快递",value:"TTKD"},{label:"百世快递",value:"HTKY"},{label:"邮政快递包裹",value:"YZPY"},{label:"EMS",value:"EMS"}]}},created(){this.orderId=parseInt(this.$route.params.id),this.fetchData()},methods:{fetchData(){this.loading=!0,Object(l["a"])(this.orderId).then(t=>{const{order_info:s,user_info:e,goods_list:a,logistics_info:i}=t.data;this.orderInfo=s,this.userInfo=e,this.goodsList=a,i&&i.logistics_no&&(this.logisticsInfo=i,this.hasLogistics=!0),this.loading=!1}).catch(()=>{this.loading=!1})},getOrderStatusType(t){const s={0:"info",1:"primary",2:"warning",3:"success",4:"danger"};return s[t]||"info"},getOrderStatusText(t){const s={0:"待支付",1:"待发货",2:"待收货",3:"已完成",4:"已取消"};return s[t]||"未知状态"},goBack(){this.$router.push("/mall/order")},viewUser(){this.$router.push("/user/detail/"+this.orderInfo.user_id)},handleShip(){this.shipDialogVisible=!0},confirmShip(){this.$refs.shipForm.validate(t=>{t&&Object(l["c"])(this.orderId,{status:2,logistics_company:this.shipForm.logistics_company,logistics_no:this.shipForm.logistics_no}).then(t=>{this.$message.success("发货成功"),this.shipDialogVisible=!1,this.fetchData()}).catch(()=>{this.$message.error("发货失败")})})},handleCancel(){this.$confirm("确认要取消该订单吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(l["d"])(this.orderId,{status:4}).then(t=>{this.$message.success("订单取消成功"),this.fetchData()}).catch(()=>{this.$message.error("订单取消失败")})}).catch(()=>{})},handleComplete(){this.$confirm("确认要将订单标记为已完成吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(l["d"])(this.orderId,{status:3}).then(t=>{this.$message.success("订单已完成"),this.fetchData()}).catch(()=>{this.$message.error("操作失败")})}).catch(()=>{})},printOrder(){this.$message.info("打印功能暂未实现")}}},r=o,n=(e("7b7a"),e("2877")),c=Object(n["a"])(r,a,i,!1,null,"2203e5c1",null);s["default"]=c.exports},"7b7a":function(t,s,e){"use strict";e("ab0d")},ab0d:function(t,s,e){},f8b7:function(t,s,e){"use strict";e.d(s,"b",(function(){return i})),e.d(s,"a",(function(){return l})),e.d(s,"d",(function(){return o})),e.d(s,"c",(function(){return r}));var a=e("b775");function i(t){return Object(a["a"])({url:"/api/v1/admin/order/list",method:"get",params:t})}function l(t){return Object(a["a"])({url:"/api/v1/admin/order/detail/"+t,method:"get"})}function o(t,s){return Object(a["a"])({url:"/api/v1/admin/order/status/"+t,method:"put",data:s})}function r(t,s){return Object(a["a"])({url:"/api/v1/admin/order/logistics/"+t,method:"put",data:s})}}}]);