# WiFi共享管理后台 - 宝塔面板部署指南

## 🚨 当前问题
前端已正确配置，但后端服务无法访问，需要配置Nginx反向代理。

## 📋 解决步骤

### 1. 上传前端文件
1. 将 `dist` 目录下的所有文件上传到宝塔面板网站根目录
2. 确保 `index.html` 在网站根目录

### 2. 配置Nginx反向代理
在宝塔面板中：

1. **进入网站设置**
   - 找到您的网站（101.37.255.139）
   - 点击"设置"

2. **修改Nginx配置**
   - 点击"配置文件"
   - 将 `nginx-config.conf` 中的内容替换到配置文件中
   - 注意修改以下路径：
     ```nginx
     root /www/wwwroot/您的网站目录;
     ```

3. **重启Nginx**
   - 保存配置后重启Nginx服务

### 3. 启动后端服务
确保后端服务在服务器上运行：

1. **检查后端是否运行**
   ```bash
   # 检查4000端口是否被占用
   netstat -tlnp | grep 4000
   ```

2. **启动后端服务**
   ```bash
   # 进入后端项目目录
   cd /path/to/wifi-share-server
   
   # 安装依赖（如果还没安装）
   npm install
   
   # 启动服务
   npm start
   # 或者使用PM2管理
   pm2 start app.js --name wifi-share-server
   ```

### 4. 开放端口（如果需要）
在宝塔面板中：
1. 进入"安全"页面
2. 添加端口规则：开放4000端口
3. 如果使用阿里云/腾讯云，还需要在安全组中开放4000端口

### 5. 测试访问
1. 访问：`http://101.37.255.139/login`
2. 使用账号：`mrx0927`，密码：`hh20250701`

## 🔧 配置说明

### 前端配置
- 现在使用相对路径访问API：`/api/v1/admin/auth/admin-login`
- Nginx会将 `/api/` 请求代理到后端服务

### 后端要求
- 后端服务必须在 `127.0.0.1:4000` 运行
- 确保后端支持跨域请求
- API路径应为：`/api/v1/admin/auth/admin-login`

## 🚀 验证步骤
1. 检查前端文件是否正确上传
2. 检查Nginx配置是否生效
3. 检查后端服务是否运行
4. 测试登录功能

## 📞 如果仍有问题
请检查：
1. 宝塔面板错误日志
2. Nginx错误日志
3. 后端服务日志
4. 浏览器开发者工具网络面板
