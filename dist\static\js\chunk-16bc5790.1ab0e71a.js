(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-16bc5790"],{5536:function(t,e,a){},"5b79":function(t,e,a){"use strict";a("5536")},6418:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{staticClass:"detail-header"},[e("el-page-header",{attrs:{content:t.userInfo.nickname||"钱包详情"},on:{back:t.goBack}})],1),e("el-card",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("用户信息")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text",icon:"el-icon-edit-outline"},on:{click:t.handleAdjust}},[t._v(" 调整余额 ")])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("div",{staticClass:"user-profile"},[e("el-avatar",{attrs:{src:t.userInfo.avatar,size:80}},[t._v(" "+t._s(t.userInfo.nickname?t.userInfo.nickname.charAt(0):"U")+" ")]),e("div",{staticClass:"user-info"},[e("h3",[t._v(t._s(t.userInfo.nickname||"未设置昵称"))]),e("p",[t._v(t._s(t.userInfo.phone||"未绑定手机"))]),"leader"===t.userInfo.role?e("el-tag",{attrs:{type:"warning"}},[t._v("团长")]):"admin"===t.userInfo.role?e("el-tag",{attrs:{type:"danger"}},[t._v("管理员")]):e("el-tag",{attrs:{type:"info"}},[t._v("普通用户")])],1)],1)]),e("el-col",{attrs:{span:16}},[e("el-descriptions",{attrs:{column:2,border:""}},[e("el-descriptions-item",{attrs:{label:"用户ID"}},[t._v(t._s(t.userInfo.id))]),e("el-descriptions-item",{attrs:{label:"当前余额"}},[e("span",{staticClass:"balance-amount"},[t._v("¥"+t._s(t.formatNumber(t.userInfo.balance)))])]),e("el-descriptions-item",{attrs:{label:"累计收益"}},[e("span",{staticClass:"income-amount"},[t._v("¥"+t._s(t.formatNumber(t.userInfo.total_income)))])]),e("el-descriptions-item",{attrs:{label:"账户状态"}},[1===t.userInfo.status?e("el-tag",{attrs:{type:"success"}},[t._v("正常")]):e("el-tag",{attrs:{type:"danger"}},[t._v("禁用")])],1),e("el-descriptions-item",{attrs:{label:"注册时间"}},[t._v(t._s(t.userInfo.created_at))]),e("el-descriptions-item",{attrs:{label:"最后更新"}},[t._v(t._s(t.userInfo.updated_at))])],1)],1)],1)],1),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("交易统计")])]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-title"},[t._v("总交易次数")]),e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.total_transactions||0))])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-title"},[t._v("收入次数")]),e("div",{staticClass:"stat-value income"},[t._v(t._s(t.stats.income_count||0))])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-title"},[t._v("支出次数")]),e("div",{staticClass:"stat-value expense"},[t._v(t._s(t.stats.expense_count||0))])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-title"},[t._v("净收益")]),e("div",{staticClass:"stat-value"},[t._v("¥"+t._s(t.formatNumber((t.stats.total_income||0)-(t.stats.total_expense||0))))])])])],1)],1),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("最近交易记录")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text",icon:"el-icon-more"},on:{click:t.viewAllTransactions}},[t._v(" 查看全部 ")])],1),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.recentTransactions,border:""}},[e("el-table-column",{attrs:{prop:"id",label:"交易ID",width:"80"}}),e("el-table-column",{attrs:{prop:"type",label:"类型",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return["income"===a.row.type?e("el-tag",{attrs:{type:"success"}},[t._v("收入")]):e("el-tag",{attrs:{type:"warning"}},[t._v("支出")])]}}])}),e("el-table-column",{attrs:{prop:"amount",label:"金额",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{class:"income"===a.row.type?"income-amount":"expense-amount"},[t._v(" "+t._s("income"===a.row.type?"+":"-")+"¥"+t._s(t.formatNumber(a.row.amount))+" ")])]}}])}),e("el-table-column",{attrs:{prop:"description",label:"描述","min-width":"200"}}),e("el-table-column",{attrs:{prop:"business_type",label:"业务类型",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return["wifi_share"===a.row.business_type?e("el-tag",{attrs:{type:"primary"}},[t._v("WiFi分享")]):"goods_sale"===a.row.business_type?e("el-tag",{attrs:{type:"success"}},[t._v("商品销售")]):"advertisement"===a.row.business_type?e("el-tag",{attrs:{type:"warning"}},[t._v("广告点击")]):"admin_adjust"===a.row.business_type?e("el-tag",{attrs:{type:"danger"}},[t._v("管理员调整")]):e("el-tag",{attrs:{type:"info"}},[t._v("其他")])]}}])}),e("el-table-column",{attrs:{prop:"created_at",label:"交易时间",width:"160"}})],1),t.recentTransactions&&0!==t.recentTransactions.length?t._e():e("div",{staticClass:"empty-block"},[e("span",{staticClass:"empty-text"},[t._v("暂无交易记录")])])],1),e("el-dialog",{attrs:{title:"调整余额",visible:t.adjustDialogVisible,width:"500px"},on:{"update:visible":function(e){t.adjustDialogVisible=e},close:t.resetAdjustForm}},[e("el-form",{ref:"adjustForm",attrs:{model:t.adjustForm,rules:t.adjustRules,"label-width":"100px",size:"small"}},[e("el-form-item",{attrs:{label:"当前余额"}},[e("span",{staticClass:"balance-amount"},[t._v("¥"+t._s(t.formatNumber(t.userInfo.balance)))])]),e("el-form-item",{attrs:{label:"调整类型",prop:"type"}},[e("el-radio-group",{model:{value:t.adjustForm.type,callback:function(e){t.$set(t.adjustForm,"type",e)},expression:"adjustForm.type"}},[e("el-radio",{attrs:{label:"increase"}},[t._v("增加余额")]),e("el-radio",{attrs:{label:"decrease"}},[t._v("减少余额")])],1)],1),e("el-form-item",{attrs:{label:"调整金额",prop:"amount"}},[e("el-input",{attrs:{placeholder:"请输入调整金额",type:"number",step:"0.01",min:"0"},model:{value:t.adjustForm.amount,callback:function(e){t.$set(t.adjustForm,"amount",e)},expression:"adjustForm.amount"}},[e("template",{slot:"prepend"},[t._v("¥")])],2)],1),e("el-form-item",{attrs:{label:"调整说明",prop:"description"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入调整说明"},model:{value:t.adjustForm.description,callback:function(e){t.$set(t.adjustForm,"description",e)},expression:"adjustForm.description"}})],1),e("el-form-item",{attrs:{label:"调整后余额"}},[e("span",{staticClass:"preview-balance"},[t._v("¥"+t._s(t.previewBalance))])])],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.adjustDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary",loading:t.adjusting},on:{click:t.submitAdjust}},[t._v("确 定")])],1)],1)],1)},r=[],i=(a("14d9"),a("b933")),l={name:"WalletDetail",data(){return{userId:null,loading:!1,userInfo:{},stats:{},recentTransactions:[],adjustDialogVisible:!1,adjusting:!1,adjustForm:{type:"increase",amount:"",description:""},adjustRules:{type:[{required:!0,message:"请选择调整类型",trigger:"change"}],amount:[{required:!0,message:"请输入调整金额",trigger:"blur"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入有效的金额",trigger:"blur"}]}}},computed:{previewBalance(){const t=parseFloat(this.userInfo.balance||0),e=parseFloat(this.adjustForm.amount||0);return"increase"===this.adjustForm.type?this.formatNumber(t+e):this.formatNumber(Math.max(0,t-e))}},created(){this.userId=this.$route.params.userId,this.fetchData()},methods:{async fetchData(){if(this.userId){this.loading=!0;try{const{data:t}=await Object(i["d"])(this.userId);this.userInfo=t.user,this.stats=t.stats,this.recentTransactions=t.recent_transactions}catch(t){console.error("获取钱包详情失败:",t),this.$message.error("获取钱包详情失败")}finally{this.loading=!1}}},goBack(){this.$router.push("/wallet/list")},viewAllTransactions(){this.$router.push("/wallet/transactions/"+this.userId)},handleAdjust(){this.adjustDialogVisible=!0,this.adjustForm={type:"increase",amount:"",description:""}},resetAdjustForm(){this.$refs.adjustForm&&this.$refs.adjustForm.resetFields()},submitAdjust(){this.$refs.adjustForm.validate(async t=>{if(t){this.adjusting=!0;try{await Object(i["a"])(this.userId,{type:this.adjustForm.type,amount:this.adjustForm.amount,description:this.adjustForm.description}),this.$message.success("余额调整成功"),this.adjustDialogVisible=!1,this.fetchData()}catch(e){this.$message.error("余额调整失败")}finally{this.adjusting=!1}}})},formatNumber(t){return parseFloat(t||0).toFixed(2)}}},n=l,o=(a("5b79"),a("2877")),c=Object(o["a"])(n,s,r,!1,null,"cc226456",null);e["default"]=c.exports},b933:function(t,e,a){"use strict";a.d(e,"e",(function(){return r})),a.d(e,"d",(function(){return i})),a.d(e,"a",(function(){return l})),a.d(e,"c",(function(){return n})),a.d(e,"b",(function(){return o}));var s=a("b775");function r(t){return Object(s["a"])({url:"/api/v1/admin/wallet/list",method:"get",params:t})}function i(t){return Object(s["a"])({url:"/api/v1/admin/wallet/detail/"+t,method:"get"})}function l(t,e){return Object(s["a"])({url:"/api/v1/admin/wallet/adjust/"+t,method:"post",data:e})}function n(t,e){return Object(s["a"])({url:"/api/v1/admin/wallet/transactions/"+t,method:"get",params:e})}function o(t){return Object(s["a"])({url:"/api/v1/admin/wallet/adjust",method:"post",data:t})}}}]);