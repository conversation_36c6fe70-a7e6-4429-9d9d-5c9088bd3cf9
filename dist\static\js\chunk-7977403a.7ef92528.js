(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7977403a"],{"7a9d":function(t,a,e){},"92c2":function(t,a,e){"use strict";e.d(a,"g",(function(){return o})),e.d(a,"f",(function(){return l})),e.d(a,"k",(function(){return c})),e.d(a,"l",(function(){return d})),e.d(a,"c",(function(){return u})),e.d(a,"m",(function(){return m})),e.d(a,"d",(function(){return f})),e.d(a,"i",(function(){return v})),e.d(a,"h",(function(){return _})),e.d(a,"a",(function(){return b})),e.d(a,"b",(function(){return g})),e.d(a,"e",(function(){return C})),e.d(a,"j",(function(){return w}));e("14d9"),e("e9f5"),e("ab43");var s=e("b775");const i=!1,r="wifi_admin_user_tags",n=[{id:1,name:"VIP用户",description:"重要VIP客户",created_at:"2023-06-10 10:30:45"},{id:2,name:"新用户",description:"注册不满30天的用户",created_at:"2023-06-08 14:20:30"},{id:3,name:"商家",description:"拥有商铺的用户",created_at:"2023-06-05 09:15:00"},{id:4,name:"活跃用户",description:"近30天有登录的用户",created_at:"2023-06-01 16:40:20"}];function o(t){return i?new Promise(a=>{setTimeout(()=>{const e=[],s=10*t.pageSize||100;for(let a=1;a<=Math.min(t.pageSize||10,s);a++){const s=(t.pageNum-1)*(t.pageSize||10),i=s+a;e.push({id:i,openid:"oMKLx5M2xxxxxxxx"+i,nickname:["张三","李四","王五","赵六","刘七"][a%5]||"用户"+i,avatar:"/img/default-avatar.png",gender:a%3,phone:"1380013800"+a,balance:parseFloat((500*Math.random()).toFixed(2)),team_id:a%3===0?1:a%3===1?2:null,is_leader:a%7===0?1:0,level:a%5,status:a%10===0?0:1,created_at:`2023-05-${String(a%30+1).padStart(2,"0")} 10:20:30`,updated_at:`2023-06-${String(a%28+1).padStart(2,"0")} 15:30:45`})}a({code:200,data:{list:e,total:s},message:"获取用户列表成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/list",method:"get",params:t})}function l(t){return i?new Promise(a=>{setTimeout(()=>{const e=parseInt(t),s={id:e,openid:"oMKLx5M2xxxxxxxx"+t,nickname:["","张三","李四","王五","赵六"][e]||"用户"+t,avatar:"/img/default-avatar.png",gender:e%2===0?2:1,phone:"1380013800"+t,balance:parseFloat((500*Math.random()).toFixed(2)),team_id:e<=2?1:3===e?2:null,parent_id:2===e?1:null,is_leader:[1,3].includes(e)?1:0,level:[1,3].includes(e)?2:2===e?1:0,status:4===e?0:1,created_at:`2023-05-0${e>4?"1":e} 10:20:30`,updated_at:`2023-05-0${e>4?"1":e} 15:30:45`};let i=null;s.team_id&&(i={id:s.team_id,name:"团队"+s.team_id,member_count:Math.floor(20*Math.random())+5,wifi_count:Math.floor(10*Math.random())+2,total_profit:parseFloat((5e3*Math.random()).toFixed(2))});const r=[{id:10*e+1,title:s.nickname+"的WiFi-1",name:`Wifi_${e}_1`,use_count:Math.floor(100*Math.random())+10,created_at:"2023-06-01 09:30:00"},{id:10*e+2,title:s.nickname+"的WiFi-2",name:`Wifi_${e}_2`,use_count:Math.floor(100*Math.random())+10,created_at:"2023-06-02 14:20:00"}],n=[{id:100*e+1,order_no:`WF${Date.now()}${e}01`,total_amount:parseFloat((200*Math.random()+50).toFixed(2)),status:Math.floor(4*Math.random())+1,created_at:"2023-06-01 16:45:30"},{id:100*e+2,order_no:`WF${Date.now()}${e}02`,total_amount:parseFloat((200*Math.random()+50).toFixed(2)),status:Math.floor(4*Math.random())+1,created_at:"2023-06-02 10:15:20"}];a({code:200,data:{user_info:s,team_info:i,wifi_list:r,order_list:n},message:"获取用户详情成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/detail/"+t,method:"get"})}function c(t,a){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"更新用户成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/update/"+t,method:"put",data:a})}function d(t,a){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"更新用户状态成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/status/"+t,method:"put",data:a})}function u(t){return i?new Promise(a=>{setTimeout(()=>{const e=p(),s=e.length>0?Math.max(...e.map(t=>t.id))+1:1,i={id:s,name:t.name,description:t.description||"",created_at:(new Date).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")};e.push(i),h(e),a({code:200,data:{id:s},message:"创建用户标签成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/tag/create",method:"post",data:t})}function m(t,a){return i?new Promise(e=>{setTimeout(()=>{const s=p(),i=s.findIndex(a=>a.id===parseInt(t));-1!==i?(s[i].name=a.name,s[i].description=a.description||s[i].description,h(s),e({code:200,message:"更新用户标签成功"})):e({code:404,message:"标签不存在"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/tag/update/"+t,method:"put",data:a})}function f(t){return i?new Promise(a=>{setTimeout(()=>{const e=p(),s=e.findIndex(a=>a.id===parseInt(t));-1!==s?(e.splice(s,1),h(e),a({code:200,message:"删除用户标签成功"})):a({code:404,message:"标签不存在"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/tag/delete/"+t,method:"delete"})}function p(){try{const t=localStorage.getItem(r);return t?JSON.parse(t):n}catch(t){return console.warn("读取用户标签数据失败，使用默认数据:",t),n}}function h(t){try{localStorage.setItem(r,JSON.stringify(t))}catch(a){console.error("保存用户标签数据失败:",a)}}function v(){return i?new Promise(t=>{setTimeout(()=>{const a=p();t({code:200,data:[...a],message:"获取用户标签列表成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/tag/list",method:"get"})}function _(){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_users:1256,active_users:1180,leader_users:45,today_users:23,month_users:187,total_balance:125680.5,avg_balance:106.51},message:"获取用户统计成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/stats",method:"get"})}function b(t,a){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{before_balance:100,after_balance:"add"===a.type?100+parseFloat(a.amount):100-parseFloat(a.amount)},message:"余额调整成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/balance/"+t,method:"post",data:a})}function g(t){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"批量操作成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/batch",method:"post",data:t})}function C(t){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_orders:Math.floor(50*Math.random())+10,total_amount:parseFloat((5e3*Math.random()+1e3).toFixed(2)),avg_order_amount:parseFloat((200*Math.random()+50).toFixed(2)),last_order_time:"2023-06-15 14:30:20",favorite_category:"数码产品",monthly_stats:[{month:"2023-01",orders:3,amount:450},{month:"2023-02",orders:5,amount:680.5},{month:"2023-03",orders:2,amount:320},{month:"2023-04",orders:4,amount:590.3},{month:"2023-05",orders:6,amount:780.2},{month:"2023-06",orders:3,amount:420}]},message:"获取消费统计成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/consumption-stats/"+t,method:"get"})}function w(t){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_wifi:Math.floor(20*Math.random())+5,total_scans:Math.floor(1e3*Math.random())+100,avg_scans_per_wifi:Math.floor(50*Math.random())+10,most_popular_wifi:{id:1,title:"星巴克WiFi",scan_count:156},recent_activity:[{date:"2023-06-15",scans:23},{date:"2023-06-14",scans:18},{date:"2023-06-13",scans:31},{date:"2023-06-12",scans:25},{date:"2023-06-11",scans:19}]},message:"获取WiFi统计成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/wifi-stats/"+t,method:"get"})}},ab43:function(t,a,e){"use strict";var s=e("23e7"),i=e("c65b"),r=e("59ed"),n=e("825a"),o=e("46c4"),l=e("c5cc"),c=e("9bdd"),d=e("2a62"),u=e("2baa"),m=e("f99f"),f=e("c430"),p=!f&&!u("map",(function(){})),h=!f&&!p&&m("map",TypeError),v=f||p||h,_=l((function(){var t=this.iterator,a=n(i(this.next,t)),e=this.done=!!a.done;if(!e)return c(t,this.mapper,[a.value,this.counter++],!0)}));s({target:"Iterator",proto:!0,real:!0,forced:v},{map:function(t){n(this);try{r(t)}catch(a){d(this,"throw",a)}return h?i(h,this,t):new _(o(this),{mapper:t})}})},bed5:function(t,a,e){"use strict";e("7a9d")},d124:function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t._self._c;return a("div",{staticClass:"app-container"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"user-detail"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-card",{staticClass:"user-card"},[a("div",{staticClass:"user-header"},[a("el-avatar",{attrs:{size:100,src:t.userInfo.avatar}},[a("img",{attrs:{src:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"}})]),a("h3",{staticClass:"nickname"},[t._v(t._s(t.userInfo.nickname||"未设置昵称"))]),a("div",{staticClass:"user-status"},[a("el-tag",{attrs:{type:1===t.userInfo.status?"success":"info"}},[t._v(t._s(1===t.userInfo.status?"启用":"禁用"))]),1===t.userInfo.is_leader?a("el-tag",{staticStyle:{"margin-left":"10px"},attrs:{type:"warning"}},[t._v("团长")]):t._e()],1)],1),a("div",{staticClass:"user-info-list"},[a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("用户ID:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.userInfo.id))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("微信openid:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.userInfo.openid||"无"))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("手机号码:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.userInfo.phone||"未绑定"))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("账户余额:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.userInfo.balance)+" 元")])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("用户等级:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.userInfo.level))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("注册时间:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.userInfo.created_at))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("最近更新:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.userInfo.updated_at))])])]),a("div",{staticClass:"action-buttons"},[1===t.userInfo.status?a("el-button",{attrs:{type:"warning"},on:{click:function(a){return t.handleStatusChange(0)}}},[t._v("禁用账户")]):a("el-button",{attrs:{type:"success"},on:{click:function(a){return t.handleStatusChange(1)}}},[t._v("启用账户")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleEdit}},[t._v("编辑信息")]),a("el-button",{attrs:{type:"warning"},on:{click:t.handleBalanceAdjust}},[t._v("调整余额")])],1)])],1),a("el-col",{attrs:{span:16}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("消费统计")])]),t.consumptionStats.total_orders>0?a("div",{staticClass:"stats-grid"},[a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.consumptionStats.total_orders))]),a("div",{staticClass:"stat-title"},[t._v("总订单数")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.consumptionStats.total_amount))]),a("div",{staticClass:"stat-title"},[t._v("总消费金额")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.consumptionStats.avg_order_amount))]),a("div",{staticClass:"stat-title"},[t._v("平均订单金额")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.consumptionStats.favorite_category))]),a("div",{staticClass:"stat-title"},[t._v("偏好分类")])])]):a("div",{staticClass:"no-data"},[a("el-empty",{attrs:{description:"暂无消费数据"}})],1)]),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("WiFi统计")])]),t.wifiStats.total_wifi>0?a("div",{staticClass:"stats-grid"},[a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.wifiStats.total_wifi))]),a("div",{staticClass:"stat-title"},[t._v("创建WiFi数")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.wifiStats.total_scans))]),a("div",{staticClass:"stat-title"},[t._v("总扫码次数")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.wifiStats.avg_scans_per_wifi))]),a("div",{staticClass:"stat-title"},[t._v("平均扫码次数")])]),a("div",{staticClass:"stat-item"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.wifiStats.most_popular_wifi?t.wifiStats.most_popular_wifi.title:"无"))]),a("div",{staticClass:"stat-title"},[t._v("最受欢迎WiFi")])])]):a("div",{staticClass:"no-data"},[a("el-empty",{attrs:{description:"暂无WiFi数据"}})],1)]),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("团队信息")])]),t.userInfo.team_id?a("div",{staticClass:"team-info"},[a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("团队ID:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.teamInfo.id))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("团队名称:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.teamInfo.name))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("成员数量:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.teamInfo.member_count)+" 人")])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("WiFi数量:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.teamInfo.wifi_count)+" 个")])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("总收益:")]),a("span",{staticClass:"info-value"},[t._v(t._s(t.teamInfo.total_profit)+" 元")])])]):a("div",{staticClass:"no-team"},[a("el-empty",{attrs:{description:"该用户未加入任何团队"}})],1)]),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("最近WiFi码")]),a("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.viewAllWifi}},[t._v("查看全部")])],1),t.wifiList.length>0?a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.wifiList,border:""}},[a("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),a("el-table-column",{attrs:{prop:"title",label:"WiFi标题","min-width":"150"}}),a("el-table-column",{attrs:{prop:"name",label:"WiFi名称","min-width":"150"}}),a("el-table-column",{attrs:{prop:"use_count",label:"使用次数",width:"120"}}),a("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"180"}}),a("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function({row:e}){return[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return t.viewWifi(e.id)}}},[t._v("查看")])]}}],null,!1,147815555)})],1):a("div",{staticClass:"no-data"},[a("el-empty",{attrs:{description:"暂无WiFi码数据"}})],1)],1),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("最近订单")]),a("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.viewAllOrders}},[t._v("查看全部")])],1),t.orderList.length>0?a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.orderList,border:""}},[a("el-table-column",{attrs:{prop:"order_no",label:"订单号","min-width":"180"}}),a("el-table-column",{attrs:{prop:"total_amount",label:"订单金额",width:"120"}}),a("el-table-column",{attrs:{label:"订单状态",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function({row:e}){return[a("el-tag",{attrs:{type:t.getOrderStatusType(e.status)}},[t._v(t._s(t.getOrderStatusText(e.status)))])]}}],null,!1,3451687627)}),a("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"180"}}),a("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function({row:e}){return[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return t.viewOrder(e.id)}}},[t._v("查看")])]}}],null,!1,104043132)})],1):a("div",{staticClass:"no-data"},[a("el-empty",{attrs:{description:"暂无订单数据"}})],1)],1)],1)],1)],1),a("el-dialog",{attrs:{title:"编辑用户信息",visible:t.dialogVisible,width:"500px"},on:{"update:visible":function(a){t.dialogVisible=a}}},[a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"昵称",prop:"nickname"}},[a("el-input",{model:{value:t.form.nickname,callback:function(a){t.$set(t.form,"nickname",a)},expression:"form.nickname"}})],1),a("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[a("el-input",{model:{value:t.form.phone,callback:function(a){t.$set(t.form,"phone",a)},expression:"form.phone"}})],1),a("el-form-item",{attrs:{label:"用户等级",prop:"level"}},[a("el-input-number",{attrs:{min:0,max:10},model:{value:t.form.level,callback:function(a){t.$set(t.form,"level",a)},expression:"form.level"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:t.form.status,callback:function(a){t.$set(t.form,"status",a)},expression:"form.status"}},[a("el-radio",{attrs:{label:1}},[t._v("启用")]),a("el-radio",{attrs:{label:0}},[t._v("禁用")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.dialogVisible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确定")])],1)],1),a("el-dialog",{attrs:{title:"调整用户余额",visible:t.balanceDialogVisible,width:"400px"},on:{"update:visible":function(a){t.balanceDialogVisible=a}}},[a("el-form",{ref:"balanceForm",attrs:{model:t.balanceForm,rules:t.balanceRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"当前余额"}},[a("span",[t._v(t._s(t.userInfo.balance)+" 元")])]),a("el-form-item",{attrs:{label:"操作类型",prop:"type"}},[a("el-radio-group",{model:{value:t.balanceForm.type,callback:function(a){t.$set(t.balanceForm,"type",a)},expression:"balanceForm.type"}},[a("el-radio",{attrs:{label:"add"}},[t._v("增加")]),a("el-radio",{attrs:{label:"subtract"}},[t._v("减少")])],1)],1),a("el-form-item",{attrs:{label:"调整金额",prop:"amount"}},[a("el-input-number",{attrs:{min:.01,step:.01,precision:2},model:{value:t.balanceForm.amount,callback:function(a){t.$set(t.balanceForm,"amount",a)},expression:"balanceForm.amount"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",rows:3},model:{value:t.balanceForm.remark,callback:function(a){t.$set(t.balanceForm,"remark",a)},expression:"balanceForm.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.balanceDialogVisible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.submitBalanceAdjust}},[t._v("确定")])],1)],1)],1)},i=[],r=(e("14d9"),e("92c2")),n={name:"UserDetail",data(){return{loading:!0,userInfo:{},teamInfo:{},wifiList:[],orderList:[],consumptionStats:{},wifiStats:{},dialogVisible:!1,balanceDialogVisible:!1,form:{nickname:"",phone:"",level:0,status:1},balanceForm:{type:"add",amount:0,remark:""},rules:{nickname:[{max:50,message:"昵称长度不能超过50个字符",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},balanceRules:{amount:[{required:!0,message:"请输入调整金额",trigger:"blur"},{type:"number",min:.01,message:"金额必须大于0",trigger:"blur"}]}}},created(){const t=parseInt(this.$route.params.id);console.log("用户详情页面加载，用户ID:",t),t&&!isNaN(t)?(this.fetchUserDetail(t),this.fetchConsumptionStats(t),this.fetchWifiStats(t)):(console.error("无效的用户ID:",this.$route.params.id),this.$message.error("无效的用户ID"),this.loading=!1)},methods:{fetchUserDetail(t){this.loading=!0,Object(r["f"])(t).then(t=>{if(console.log("用户详情数据:",t),t.data){const{user_info:a,team_info:e,wifi_list:s,order_list:i}=t.data;this.userInfo=a||{},this.teamInfo=e||{},this.wifiList=s||[],this.orderList=i||[]}else console.error("响应数据结构错误:",t),this.$message.error("数据格式错误");this.loading=!1}).catch(t=>{console.error("获取用户详情失败:",t),this.$message.error("获取用户详情失败"),this.loading=!1})},fetchConsumptionStats(t){Object(r["e"])(t).then(t=>{this.consumptionStats=t.data||{}}).catch(t=>{console.error("获取消费统计失败:",t)})},fetchWifiStats(t){Object(r["j"])(t).then(t=>{this.wifiStats=t.data||{}}).catch(t=>{console.error("获取WiFi统计失败:",t)})},handleEdit(){this.form={nickname:this.userInfo.nickname,phone:this.userInfo.phone,level:this.userInfo.level,status:this.userInfo.status},this.dialogVisible=!0},handleBalanceAdjust(){this.balanceForm={type:"add",amount:0,remark:""},this.balanceDialogVisible=!0},submitForm(){this.$refs.form.validate(t=>{if(t){if(!this.userInfo.id)return void this.$message.error("用户ID无效，无法更新");Object(r["k"])(this.userInfo.id,this.form).then(t=>{this.$message.success("更新成功"),this.dialogVisible=!1,this.fetchUserDetail(this.userInfo.id)}).catch(()=>{this.$message.error("更新失败")})}})},submitBalanceAdjust(){this.$refs.balanceForm.validate(t=>{if(t){if(!this.userInfo.id)return void this.$message.error("用户ID无效，无法调整余额");Object(r["a"])(this.userInfo.id,this.balanceForm).then(t=>{this.$message.success("余额调整成功"),this.balanceDialogVisible=!1,this.fetchUserDetail(this.userInfo.id)}).catch(()=>{this.$message.error("余额调整失败")})}})},handleStatusChange(t){this.userInfo.id?this.$confirm(`确认要${1===t?"启用":"禁用"}该用户吗?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(r["l"])(this.userInfo.id,{status:t}).then(a=>{this.$message.success("状态更新成功"),this.userInfo.status=t}).catch(()=>{this.$message.error("状态更新失败")})}).catch(()=>{}):this.$message.error("用户ID无效，无法更新状态")},viewWifi(t){this.$router.push("/wifi/detail/"+t)},viewAllWifi(){this.$router.push({path:"/wifi/list",query:{user_id:this.userInfo.id}})},viewOrder(t){this.$router.push("/mall/order/detail/"+t)},viewAllOrders(){this.$router.push({path:"/mall/order",query:{user_id:this.userInfo.id}})},getOrderStatusType(t){const a={0:"info",1:"primary",2:"warning",3:"success",4:"danger"};return a[t]||"info"},getOrderStatusText(t){const a={0:"待支付",1:"待发货",2:"待收货",3:"已完成",4:"已取消"};return a[t]||"未知状态"}}},o=n,l=(e("bed5"),e("2877")),c=Object(l["a"])(o,s,i,!1,null,"14951380",null);a["default"]=c.exports}}]);