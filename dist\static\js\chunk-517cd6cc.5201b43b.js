(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-517cd6cc"],{"404b":function(e,t,a){"use strict";a("fcdf")},ac98:function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"d",(function(){return s})),a.d(t,"b",(function(){return n})),a.d(t,"j",(function(){return o})),a.d(t,"c",(function(){return l})),a.d(t,"g",(function(){return d})),a.d(t,"f",(function(){return m})),a.d(t,"a",(function(){return c})),a.d(t,"i",(function(){return u})),a.d(t,"h",(function(){return f}));var r=a("b775");function i(e){return Object(r["a"])({url:"/api/v1/admin/team/list",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/api/v1/admin/team/detail/"+e,method:"get"})}function n(e){return Object(r["a"])({url:"/api/v1/admin/team/create",method:"post",data:e})}function o(e,t){return Object(r["a"])({url:"/api/v1/admin/team/update/"+e,method:"put",data:t})}function l(e){return Object(r["a"])({url:"/api/v1/admin/team/delete/"+e,method:"delete"})}function d(e){return Object(r["a"])({url:"/api/v1/admin/team/stats/"+e,method:"get"})}function m(e,t){return Object(r["a"])({url:`/api/v1/admin/team/${e}/members`,method:"get",params:t})}function c(e,t){return Object(r["a"])({url:`/api/v1/admin/team/${e}/members`,method:"post",data:{user_id:t}})}function u(e,t){return Object(r["a"])({url:`/api/v1/admin/team/${e}/members/${t}`,method:"delete"})}function f(e,t){return u(e,t)}},bce4:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"team-form-container"},[t("el-card",[t("div",{attrs:{slot:"header"},slot:"header"},[e._v(" "+e._s(e.isEdit?"编辑团队":"创建团队")+" ")]),t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"团队名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"请输入团队名称",maxlength:"50"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),e.isEdit?e._e():t("el-form-item",{attrs:{label:"选择团长",prop:"leader_id"}},[t("el-select",{attrs:{placeholder:"请选择团长",filterable:"",remote:"","remote-method":e.searchUsers,loading:e.userLoading},model:{value:e.form.leader_id,callback:function(t){e.$set(e.form,"leader_id",t)},expression:"form.leader_id"}},e._l(e.userList,(function(a){return t("el-option",{key:a.id,attrs:{label:`${a.nickname} (${a.phone})`,value:a.id}},[t("div",{staticClass:"user-option"},[t("el-avatar",{attrs:{size:24,src:a.avatar||"/img/default-avatar.png"}}),t("span",{staticClass:"user-info"},[e._v(e._s(a.nickname)+" - "+e._s(a.phone))])],1)])})),1)],1),t("el-form-item",{attrs:{label:"团队简介",prop:"description"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入团队简介",rows:4,maxlength:"200","show-word-limit":""},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1),t("el-form-item",{attrs:{label:"分润比例",prop:"profit_ratio"}},[t("el-input-number",{attrs:{min:0,max:100,precision:2,step:.1},model:{value:e.form.profit_ratio,callback:function(t){e.$set(e.form,"profit_ratio",t)},expression:"form.profit_ratio"}}),t("span",{staticClass:"input-suffix"},[e._v("%")])],1),t("el-form-item",{attrs:{label:"团队状态",prop:"status"}},[t("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[t("el-radio",{attrs:{label:1}},[e._v("正常")]),t("el-radio",{attrs:{label:0}},[e._v("禁用")])],1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v(e._s(e.isEdit?"保存修改":"创建团队"))]),t("el-button",{on:{click:e.handleCancel}},[e._v("取消")])],1)],1)],1)],1)},i=[],s=(a("14d9"),a("ac98")),n=a("c24f"),o={name:"TeamForm",data(){return{loading:!1,userLoading:!1,isEdit:!1,teamId:null,form:{name:"",leader_id:null,description:"",profit_ratio:10,status:1},rules:{name:[{required:!0,message:"请输入团队名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],leader_id:[{required:!0,message:"请选择团长",trigger:"change"}],profit_ratio:[{required:!0,message:"请设置分润比例",trigger:"blur"}]},userList:[]}},created(){this.teamId=this.$route.params.id,this.isEdit=!!this.teamId,this.isEdit&&this.fetchTeamDetail()},methods:{async fetchTeamDetail(){this.loading=!0;try{const e=await Object(s["d"])(this.teamId);if(0===e.code){const t=e.data;this.form={name:t.name,leader_id:t.leader_id,description:t.description||"",profit_ratio:t.profit_ratio||10,status:t.status},t.leader_id&&(this.userList=[{id:t.leader_id,nickname:t.leader_name,phone:t.leader_phone,avatar:t.leader_avatar}])}}catch(e){this.$message.error("获取团队信息失败")}finally{this.loading=!1}},async searchUsers(e){if(e){this.userLoading=!0;try{const t=await Object(n["d"])({keyword:e});0===t.code&&(this.userList=t.data.list||[])}catch(t){this.$message.error("搜索用户失败")}finally{this.userLoading=!1}}},handleSubmit(){this.$refs.form.validate(async e=>{if(e){this.loading=!0;try{let e;e=this.isEdit?await Object(s["j"])(this.teamId,this.form):await Object(s["b"])(this.form),0===e.code&&(this.$message.success(this.isEdit?"修改成功":"创建成功"),this.$router.push("/team/list"))}catch(t){this.$message.error(this.isEdit?"修改失败":"创建失败")}finally{this.loading=!1}}})},handleCancel(){this.$router.back()}}},l=o,d=(a("404b"),a("2877")),m=Object(d["a"])(l,r,i,!1,null,"28713820",null);t["default"]=m.exports},fcdf:function(e,t,a){}}]);