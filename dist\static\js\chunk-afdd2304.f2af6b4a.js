(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-afdd2304"],{"007d":function(t,e,a){"use strict";var r=a("22b4"),i=a("2da7");Object(r["a"])(i["a"])},"13d5":function(t,e,a){"use strict";var r=a("23e7"),i=a("d58f").left,n=a("a640"),s=a("1212"),o=a("9adc"),u=!o&&s>79&&s<83,l=u||!n("reduce");r({target:"Array",proto:!0,forced:l},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},"330e":function(t,e,a){"use strict";a("34b0")},"333d":function(t,e,a){"use strict";var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[e("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},i=[],n={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get(){return this.page},set(t){this.$emit("update:page",t)}},pageSize:{get(){return this.limit},set(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange(t){this.$emit("pagination",{page:this.currentPage,limit:t})},handleCurrentChange(t){this.$emit("pagination",{page:t,limit:this.pageSize})}}},s=n,o=(a("330e"),a("2877")),u=Object(o["a"])(s,r,i,!1,null,"11252b03",null);e["a"]=u.exports},"34b0":function(t,e,a){},"3eba":function(t,e,a){"use strict";var r=a("aa74");a.d(e,"a",(function(){return r["a"]}));var i=a("22b4"),n=(a("1be7"),a("f95e")),s=a("5e81"),o=a("ee29");Object(i["a"])([n["a"],s["a"]]);Object(i["a"])(o["a"])},"627c":function(t,e,a){"use strict";var r=a("22b4"),i=a("9394");Object(r["a"])(i["a"])},8558:function(t,e,a){"use strict";var r=a("cfe9"),i=a("b5db"),n=a("c6b6"),s=function(t){return i.slice(0,t.length)===t};t.exports=function(){return s("Bun/")?"BUN":s("Cloudflare-Workers")?"CLOUDFLARE":s("Deno/")?"DENO":s("Node.js/")?"NODE":r.Bun&&"string"==typeof Bun.version?"BUN":r.Deno&&"object"==typeof Deno.version?"DENO":"process"===n(r.process)?"NODE":r.window&&r.document?"BROWSER":"REST"}()},9485:function(t,e,a){"use strict";var r=a("23e7"),i=a("2266"),n=a("59ed"),s=a("825a"),o=a("46c4"),u=a("2a62"),l=a("f99f"),c=a("2ba4"),d=a("d039"),p=TypeError,m=d((function(){[].keys().reduce((function(){}),void 0)})),_=!m&&l("reduce",p);r({target:"Iterator",proto:!0,real:!0,forced:m||_},{reduce:function(t){s(this);try{n(t)}catch(d){u(this,"throw",d)}var e=arguments.length<2,a=e?void 0:arguments[1];if(_)return c(_,this,e?[t]:[t,a]);var r=o(this),l=0;if(i(r,(function(r){e?(e=!1,a=r):a=t(a,r,l),l++}),{IS_RECORD:!0}),e)throw new p("Reduce of empty iterator with no initial value");return a}})},"9a63":function(t,e,a){},"9adc":function(t,e,a){"use strict";var r=a("8558");t.exports="NODE"===r},a640:function(t,e,a){"use strict";var r=a("d039");t.exports=function(t,e){var a=[][t];return!!a&&r((function(){a.call(null,e||function(){return 1},1)}))}},a7de:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{staticClass:"filter-container"},[e("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"用户昵称/手机号",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.keyword,callback:function(e){t.$set(t.listQuery,"keyword",e)},expression:"listQuery.keyword"}}),e("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"分润来源",clearable:""},model:{value:t.listQuery.source_type,callback:function(e){t.$set(t.listQuery,"source_type",e)},expression:"listQuery.source_type"}},t._l(t.sourceTypeOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),e("el-date-picker",{staticClass:"filter-item",staticStyle:{width:"240px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},on:{change:t.handleDateRangeChange},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}}),e("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleFilter}},[t._v("搜索")])],1),e("el-tabs",{on:{"tab-click":t.handleTabClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[e("el-tab-pane",{attrs:{label:"全部",name:"all"}}),e("el-tab-pane",{attrs:{label:"WiFi分润",name:"wifi"}}),e("el-tab-pane",{attrs:{label:"商品分润",name:"goods"}}),e("el-tab-pane",{attrs:{label:"广告分润",name:"ad"}})],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{data:t.list,"element-loading-text":"正在加载...",border:"",fit:"","highlight-current-row":""}},[e("el-table-column",{attrs:{label:"ID",prop:"id",align:"center",width:"80"}}),e("el-table-column",{attrs:{label:"用户信息",align:"center","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("div",{staticClass:"user-info"},[e("el-avatar",{attrs:{size:32,src:a.user_avatar}},[e("img",{attrs:{src:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"}})]),e("div",{staticClass:"user-detail"},[e("div",{staticClass:"nickname"},[t._v(t._s(a.user_nickname))]),e("div",{staticClass:"phone"},[t._v(t._s(a.user_phone))])])],1)]}}])}),e("el-table-column",{attrs:{label:"分润金额",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("span",{staticClass:"profit-amount"},[t._v("+"+t._s(a.amount)+" 元")])]}}])}),e("el-table-column",{attrs:{label:"分润类型",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-tag",{attrs:{type:t.getSourceTypeTag(a.source_type)}},[t._v(t._s(t.getSourceTypeLabel(a.source_type)))])]}}])}),e("el-table-column",{attrs:{label:"来源ID",prop:"source_id",align:"center",width:"100"}}),e("el-table-column",{attrs:{label:"备注",prop:"remark",align:"center","min-width":"180"}}),e("el-table-column",{attrs:{label:"时间",prop:"created_at",align:"center",width:"160"}}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"120","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.handleView(a)}}},[t._v("详情")])]}}])})],1),e("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("分润统计")])]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-title"},[t._v("今日分润")]),e("div",{staticClass:"stat-value"},[t._v("¥ "+t._s(t.stats.today||0))])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-title"},[t._v("本周分润")]),e("div",{staticClass:"stat-value"},[t._v("¥ "+t._s(t.stats.week||0))])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-title"},[t._v("本月分润")]),e("div",{staticClass:"stat-value"},[t._v("¥ "+t._s(t.stats.month||0))])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-card"},[e("div",{staticClass:"stat-title"},[t._v("累计分润")]),e("div",{staticClass:"stat-value"},[t._v("¥ "+t._s(t.stats.total||0))])])])],1),e("el-row",{staticStyle:{"margin-top":"20px"}},[e("el-col",{attrs:{span:24}},[e("div",{staticStyle:{width:"100%",height:"300px"},attrs:{id:"profit-chart"}})])],1)],1)],1)},i=[],n=(a("14d9"),a("e9f5"),a("ab43"),a("f851")),s=a("333d"),o=a("3eba"),u=(a("ef97"),a("22b4")),l=a("49bb");Object(u["a"])(l["a"]);a("007d"),a("627c"),a("d28f"),a("cd12");var c={name:"ProfitBillList",components:{Pagination:s["a"]},data(){return{activeTab:"all",list:[],total:0,listLoading:!0,listQuery:{page:1,limit:10,keyword:void 0,source_type:void 0,start_date:void 0,end_date:void 0},dateRange:null,sourceTypeOptions:[{label:"WiFi分润",value:1},{label:"商品分润",value:2},{label:"广告分润",value:3}],stats:{today:0,week:0,month:0,total:0},chart:null}},created(){this.getList()},mounted(){this.$nextTick(()=>{this.initChart()})},beforeDestroy(){this.chart&&(this.chart.dispose(),this.chart=null)},methods:{getList(){this.listLoading=!0,Object(n["d"])(this.listQuery).then(t=>{const{list:e,total:a,stats:r}=t.data;this.list=e||[],this.total=t.data.pagination&&t.data.pagination.total||t.data.total||0,this.stats=r||{},this.listLoading=!1,this.chart&&this.updateChart(r.chart_data)}).catch(()=>{this.listLoading=!1})},handleFilter(){this.listQuery.page=1,this.getList()},handleTabClick(){switch(this.activeTab){case"wifi":this.listQuery.source_type=1;break;case"goods":this.listQuery.source_type=2;break;case"ad":this.listQuery.source_type=3;break;default:this.listQuery.source_type=void 0}this.handleFilter()},handleDateRangeChange(t){t?(this.listQuery.start_date=t[0],this.listQuery.end_date=t[1]):(this.listQuery.start_date=void 0,this.listQuery.end_date=void 0)},handleView(t){this.$router.push("/profit/bill/detail/"+t.id)},getSourceTypeLabel(t){const e={1:"WiFi分润",2:"商品分润",3:"广告分润"};return e[t]||"未知"},getSourceTypeTag(t){const e={1:"primary",2:"success",3:"warning"};return e[t]||"info"},initChart(){this.chart=o["a"](document.getElementById("profit-chart"));const t={title:{text:"分润趋势",left:"center"},tooltip:{trigger:"axis",formatter:"{b}: {c} 元"},xAxis:{type:"category",data:[]},yAxis:{type:"value",name:"金额(元)"},series:[{name:"分润金额",type:"line",smooth:!0,data:[],areaStyle:{opacity:.3},itemStyle:{color:"#409EFF"}}]};this.chart.setOption(t),window.addEventListener("resize",()=>{this.chart&&this.chart.resize()})},updateChart(t){if(!t||!this.chart)return;const e=t.map(t=>t.date),a=t.map(t=>t.amount);this.chart.setOption({xAxis:{data:e},series:[{data:a}]})}}},d=c,p=(a("fdba"),a("2877")),m=Object(p["a"])(d,r,i,!1,null,"a3287b5c",null);e["default"]=m.exports},ab43:function(t,e,a){"use strict";var r=a("23e7"),i=a("c65b"),n=a("59ed"),s=a("825a"),o=a("46c4"),u=a("c5cc"),l=a("9bdd"),c=a("2a62"),d=a("2baa"),p=a("f99f"),m=a("c430"),_=!m&&!d("map",(function(){})),h=!m&&!_&&p("map",TypeError),f=m||_||h,g=u((function(){var t=this.iterator,e=s(i(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));r({target:"Iterator",proto:!0,real:!0,forced:f},{map:function(t){s(this);try{n(t)}catch(e){c(this,"throw",e)}return h?i(h,this,t):new g(o(this),{mapper:t})}})},cd12:function(t,e,a){"use strict";var r=a("22b4"),i=a("4b2a");Object(r["a"])(i["a"])},d28f:function(t,e,a){"use strict";var r=a("22b4"),i=a("ff32");Object(r["a"])(i["a"])},d58f:function(t,e,a){"use strict";var r=a("59ed"),i=a("7b0b"),n=a("44ad"),s=a("07fa"),o=TypeError,u="Reduce of empty array with no initial value",l=function(t){return function(e,a,l,c){var d=i(e),p=n(d),m=s(d);if(r(a),0===m&&l<2)throw new o(u);var _=t?m-1:0,h=t?-1:1;if(l<2)while(1){if(_ in p){c=p[_],_+=h;break}if(_+=h,t?_<0:m<=_)throw new o(u)}for(;t?_>=0:m>_;_+=h)_ in p&&(c=a(c,p[_],_,d));return c}};t.exports={left:l(!1),right:l(!0)}},ef97:function(t,e,a){"use strict";var r=a("22b4"),i=a("3620");Object(r["a"])(i["a"])},f665:function(t,e,a){"use strict";var r=a("23e7"),i=a("c65b"),n=a("2266"),s=a("59ed"),o=a("825a"),u=a("46c4"),l=a("2a62"),c=a("f99f"),d=c("find",TypeError);r({target:"Iterator",proto:!0,real:!0,forced:d},{find:function(t){o(this);try{s(t)}catch(r){l(this,"throw",r)}if(d)return i(d,this,t);var e=u(this),a=0;return n(e,(function(e,r){if(t(e,a++))return r(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},f851:function(t,e,a){"use strict";a.d(e,"e",(function(){return f})),a.d(e,"h",(function(){return g})),a.d(e,"d",(function(){return y})),a.d(e,"c",(function(){return b})),a.d(e,"g",(function(){return v})),a.d(e,"f",(function(){return w})),a.d(e,"a",(function(){return k})),a.d(e,"b",(function(){return x}));a("d9e2"),a("13d5"),a("e9f5"),a("910d"),a("f665"),a("9485");var r=a("b775");const i=!1,n="wifi_admin_profit_rules",s="wifi_admin_profit_bills",o="wifi_admin_withdraw_list",u={wifi_share:{name:"WiFi分享",user_rate:70,platform_rate:30,status:1},goods_sale:{name:"商品销售",user_rate:10,leader_rate:5,platform_rate:85,status:1},advertisement:{name:"广告点击",user_rate:20,leader_rate:10,platform_rate:70,status:1},updated_at:"2025-07-08 16:30:00"};function l(){try{const t=localStorage.getItem(n);return t?JSON.parse(t):u}catch(t){return console.warn("读取分润规则数据失败，使用默认数据:",t),u}}function c(t){try{localStorage.setItem(n,JSON.stringify(t))}catch(e){console.error("保存分润规则数据失败:",e)}}const d=[{id:101,bill_no:"SB20250708001",type:"wifi_share",amount:.6,share_amount:.42,platform_amount:.18,user_id:1001,user_name:"张三",user_phone:"138****1001",source_id:1,source_type:"wifi",status:1,remark:"WiFi码分享使用收益",settle_time:null,created_at:"2025-07-08 10:15:22"},{id:102,bill_no:"SB20250708002",type:"wifi_share",amount:.4,share_amount:.28,platform_amount:.12,user_id:1002,user_name:"李四",user_phone:"138****1002",source_id:2,source_type:"wifi",status:2,remark:"WiFi码分享使用收益",settle_time:"2025-07-08 16:32:45",created_at:"2025-07-08 09:27:18"},{id:103,bill_no:"SB20250708003",type:"goods_sale",amount:25,share_amount:2.5,platform_amount:22.5,user_id:1003,user_name:"王五",user_phone:"138****1003",source_id:1,source_type:"order",status:1,remark:"商品销售分润",settle_time:null,created_at:"2025-07-08 14:52:36"},{id:104,bill_no:"SB20250708004",type:"advertisement",amount:5.6,share_amount:1.12,platform_amount:4.48,user_id:1001,user_name:"张三",user_phone:"138****1001",source_id:3,source_type:"ad_click",status:2,remark:"广告点击收益",settle_time:"2025-07-08 18:21:05",created_at:"2025-07-08 16:05:43"}];function p(){try{const t=localStorage.getItem(s);return t?JSON.parse(t):d}catch(t){return console.warn("读取账单数据失败，使用默认数据:",t),d}}const m=[{id:1,withdraw_no:"W2025070800001",user_id:1001,user_name:"张三",user_phone:"138****1001",amount:200,status:0,type:1,account_type:"支付宝",account_name:"张三",account_no:"<EMAIL>",bank_name:null,created_at:"2025-07-08 10:12:33",audit_time:null,audit_user:null,audit_remark:null,pay_time:null,pay_remark:null},{id:2,withdraw_no:"W2025070800002",user_id:1002,user_name:"李四",user_phone:"138****1002",amount:500,status:1,type:2,account_type:"银行卡",account_name:"李四",account_no:"6222xxxxxxx",bank_name:"工商银行",created_at:"2025-07-08 09:45:21",audit_time:"2025-07-08 11:23:45",audit_user:"admin",audit_remark:"审核通过",pay_time:null,pay_remark:null},{id:3,withdraw_no:"W2025070800003",user_id:1003,user_name:"王五",user_phone:"138****1003",amount:100,status:2,type:1,account_type:"支付宝",account_name:"王五",account_no:"<EMAIL>",bank_name:null,created_at:"2025-07-07 16:32:18",audit_time:"2025-07-08 09:15:30",audit_user:"admin",audit_remark:"金额不足，请重新提交",pay_time:null,pay_remark:null},{id:4,withdraw_no:"W2025070800004",user_id:1004,user_name:"赵六",user_phone:"138****1004",amount:300,status:3,type:2,account_type:"银行卡",account_name:"赵六",account_no:"6217xxxxxxx",bank_name:"招商银行",created_at:"2025-07-07 14:56:42",audit_time:"2025-07-07 16:28:35",audit_user:"admin",audit_remark:"审核通过",pay_time:"2025-07-08 10:35:12",pay_remark:"打款成功"}];function _(){try{const t=localStorage.getItem(o);return t?JSON.parse(t):m}catch(t){return console.warn("读取提现数据失败，使用默认数据:",t),m}}function h(t){try{localStorage.setItem(o,JSON.stringify(t))}catch(e){console.error("保存提现数据失败:",e)}}function f(){return i?new Promise(t=>{setTimeout(()=>{const e=l();t({code:200,data:e,message:"获取成功"})},200)}):Object(r["a"])({url:"/api/v1/admin/income/rules",method:"get"})}function g(t){return i?new Promise(e=>{setTimeout(()=>{const a=l(),r=Object.assign(a,t,{updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)});c(r),e({code:200,message:"更新成功"})},500)}):Object(r["a"])({url:"/api/v1/admin/income/rules/update",method:"post",data:t})}function y(t){return i?new Promise(e=>{setTimeout(()=>{const a=p(),r=parseInt(t.page)||1,i=parseInt(t.limit)||10;let n=[...a];t.keyword&&(n=n.filter(e=>e.user_name.includes(t.keyword)||e.user_phone.includes(t.keyword)||e.bill_no.includes(t.keyword))),t.type&&(n=n.filter(e=>e.type===t.type)),void 0!==t.status&&""!==t.status&&(n=n.filter(e=>e.status===parseInt(t.status))),t.start_date&&t.end_date&&(n=n.filter(e=>{const a=new Date(e.created_at),r=new Date(t.start_date),i=new Date(t.end_date);return i.setHours(23,59,59,999),a>=r&&a<=i}));const s=n.length,o=(r-1)*i,u=o+i,l=n.slice(o,u),c=n.reduce((t,e)=>t+e.amount,0),d=n.reduce((t,e)=>t+e.share_amount,0),m=n.reduce((t,e)=>t+e.platform_amount,0);e({code:200,data:{list:l,total:s,page:r,limit:i,stats:{total_amount:c.toFixed(2),total_share_amount:d.toFixed(2),total_platform_amount:m.toFixed(2)}},message:"获取成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/income/bill/list",method:"get",params:t})}function b(t){return i?new Promise((e,a)=>{setTimeout(()=>{try{const r=p(),i=r.find(e=>e.id===parseInt(t));if(!i)return void a(new Error("账单不存在"));const n={code:200,data:{detail:{id:i.id,amount:i.amount,source_type:"wifi_share"===i.type?1:"goods_sale"===i.type?2:3,source_id:i.bill_no,created_at:i.created_at,remark:i.remark},user_info:{id:i.user_id,nickname:i.user_name,phone:i.user_phone,avatar:"",is_leader:Math.random()>.5?1:0,balance:(1e3*Math.random()).toFixed(2)},source_info:"wifi_share"===i.type?{title:"WiFi示例",name:"Test_WiFi_"+i.id,merchant_name:"示例商户",use_count:Math.floor(100*Math.random())}:"goods_sale"===i.type?{order_no:i.bill_no,total_amount:i.amount,status:1,created_at:i.created_at}:{title:"广告示例",space_name:"首页广告位",click_count:Math.floor(1e3*Math.random()),view_count:Math.floor(5e3*Math.random())},profit_detail:[{role:"分享者",rate:70,amount:i.share_amount,user_info:`${i.user_name}(${i.user_phone})`},{role:"平台",rate:30,amount:i.platform_amount,user_info:"系统平台"}]},message:"success"};e(n)}catch(r){a(r)}},200)}):Object(r["a"])({url:"/api/v1/admin/income/bill/detail/"+t,method:"get"})}function v(t){return i?new Promise(e=>{setTimeout(()=>{const a=_(),r=parseInt(t.page)||1,i=parseInt(t.limit)||10;let n=[...a];t.keyword&&(n=n.filter(e=>e.user_name.includes(t.keyword)||e.user_phone.includes(t.keyword)||e.withdraw_no.includes(t.keyword))),void 0!==t.status&&""!==t.status&&(n=n.filter(e=>e.status===parseInt(t.status))),t.start_date&&t.end_date&&(n=n.filter(e=>{const a=new Date(e.created_at),r=new Date(t.start_date),i=new Date(t.end_date);return i.setHours(23,59,59,999),a>=r&&a<=i}));const s=n.length,o=(r-1)*i,u=o+i,l=n.slice(o,u);e({code:200,data:{list:l,total:s,page:r,limit:i},message:"获取成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/withdraw/list",method:"get",params:t})}function w(t){return i?new Promise((e,a)=>{setTimeout(()=>{const r=_(),i=r.find(e=>e.id===parseInt(t));if(i){const t={detail:{...i,card_holder:i.account_name,bank_name:i.bank_name||"支付宝",card_number:i.account_no,bank_branch:"工商银行"===i.bank_name?"北京市朝阳区支行":null,transfer_time:i.pay_time,transaction_id:3===i.status?"TRX202412270001":null,remark:i.audit_remark||i.pay_remark||null},user_info:{id:i.user_id,nickname:i.user_name,phone:i.user_phone,avatar:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",balance:1580.5,is_leader:101===i.user_id?1:0}};e({code:200,data:t,message:"获取成功"})}else a(new Error("提现申请不存在"))},200)}):Object(r["a"])({url:"/api/v1/admin/withdraw/detail/"+t,method:"get"})}function k(t,e){return i?new Promise((a,r)=>{setTimeout(()=>{const i=_(),n=i.findIndex(e=>e.id===parseInt(t));n>-1?(i[n].status=e.status,i[n].audit_time=(new Date).toISOString().replace("T"," ").slice(0,19),i[n].audit_user="admin",i[n].audit_remark=e.remark||(1===e.status?"审核通过":"审核拒绝"),h(i),a({code:200,message:"审核成功"})):r(new Error("提现申请不存在"))},500)}):Object(r["a"])({url:"/api/v1/admin/withdraw/audit/"+t,method:"post",data:e})}function x(t,e){return i?new Promise((a,r)=>{setTimeout(()=>{const i=_(),n=i.findIndex(e=>e.id===parseInt(t));n>-1?(i[n].status=3,i[n].pay_time=(new Date).toISOString().replace("T"," ").slice(0,19),i[n].pay_remark=e.remark||"已打款",i[n].transaction_id=e.transaction_id,h(i),a({code:200,message:"打款成功"})):r(new Error("提现申请不存在"))},500)}):Object(r["a"])({url:"/api/v1/admin/withdraw/confirm/"+t,method:"post",data:e})}},fdba:function(t,e,a){"use strict";a("9a63")}}]);