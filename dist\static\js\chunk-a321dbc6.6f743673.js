(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a321dbc6"],{"13d5":function(t,e,i){"use strict";var r=i("23e7"),a=i("d58f").left,s=i("a640"),n=i("1212"),o=i("9adc"),c=!o&&n>79&&n<83,u=c||!s("reduce");r({target:"Array",proto:!0,forced:u},{reduce:function(t){var e=arguments.length;return a(this,t,e,e>1?arguments[1]:void 0)}})},8558:function(t,e,i){"use strict";var r=i("cfe9"),a=i("b5db"),s=i("c6b6"),n=function(t){return a.slice(0,t.length)===t};t.exports=function(){return n("Bun/")?"BUN":n("Cloudflare-Workers")?"CLOUDFLARE":n("Deno/")?"DENO":n("Node.js/")?"NODE":r.Bun&&"string"==typeof Bun.version?"BUN":r.Deno&&"object"==typeof Deno.version?"DENO":"process"===s(r.process)?"NODE":r.window&&r.document?"BROWSER":"REST"}()},9485:function(t,e,i){"use strict";var r=i("23e7"),a=i("2266"),s=i("59ed"),n=i("825a"),o=i("46c4"),c=i("2a62"),u=i("f99f"),l=i("2ba4"),d=i("d039"),m=TypeError,f=d((function(){[].keys().reduce((function(){}),void 0)})),h=!f&&u("reduce",m);r({target:"Iterator",proto:!0,real:!0,forced:f||h},{reduce:function(t){n(this);try{s(t)}catch(d){c(this,"throw",d)}var e=arguments.length<2,i=e?void 0:arguments[1];if(h)return l(h,this,e?[t]:[t,i]);var r=o(this),u=0;if(a(r,(function(r){e?(e=!1,i=r):i=t(i,r,u),u++}),{IS_RECORD:!0}),e)throw new m("Reduce of empty iterator with no initial value");return i}})},"9adc":function(t,e,i){"use strict";var r=i("8558");t.exports="NODE"===r},a640:function(t,e,i){"use strict";var r=i("d039");t.exports=function(t,e){var i=[][t];return!!i&&r((function(){i.call(null,e||function(){return 1},1)}))}},ab43:function(t,e,i){"use strict";var r=i("23e7"),a=i("c65b"),s=i("59ed"),n=i("825a"),o=i("46c4"),c=i("c5cc"),u=i("9bdd"),l=i("2a62"),d=i("2baa"),m=i("f99f"),f=i("c430"),h=!f&&!d("map",(function(){})),g=!f&&!h&&m("map",TypeError),p=f||h||g,w=c((function(){var t=this.iterator,e=n(a(this.next,t)),i=this.done=!!e.done;if(!i)return u(t,this.mapper,[e.value,this.counter++],!0)}));r({target:"Iterator",proto:!0,real:!0,forced:p},{map:function(t){n(this);try{s(t)}catch(e){l(this,"throw",e)}return g?a(g,this,t):new w(o(this),{mapper:t})}})},cd65:function(t,e,i){"use strict";i("d4cc")},d0c3:function(t,e,i){"use strict";i.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"WiFi标题",prop:"title"}},[e("el-input",{attrs:{placeholder:"请输入WiFi标题"},model:{value:t.form.title,callback:function(e){t.$set(t.form,"title",e)},expression:"form.title"}})],1),e("el-form-item",{attrs:{label:"WiFi名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入WiFi名称"},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),e("el-form-item",{attrs:{label:"WiFi密码",prop:"password"}},[e("el-input",{attrs:{placeholder:"请输入WiFi密码"},model:{value:t.form.password,callback:function(e){t.$set(t.form,"password",e)},expression:"form.password"}})],1),e("el-form-item",{attrs:{label:"商户名称",prop:"merchant_name"}},[e("el-input",{attrs:{placeholder:"请输入商户名称"},model:{value:t.form.merchant_name,callback:function(e){t.$set(t.form,"merchant_name",e)},expression:"form.merchant_name"}})],1),e("el-form-item",{attrs:{label:"状态",prop:"status"}},[e("el-radio-group",{model:{value:t.form.status,callback:function(e){t.$set(t.form,"status",e)},expression:"form.status"}},[e("el-radio",{attrs:{label:1}},[t._v("启用")]),e("el-radio",{attrs:{label:0}},[t._v("禁用")])],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v(t._s(t.isEdit?"更新":"创建"))]),e("el-button",{on:{click:t.cancel}},[t._v("取消")])],1)],1)],1)},a=[],s=(i("14d9"),i("d251")),n={name:"WiFiForm",data(){return{form:{title:"",name:"",password:"",merchant_name:"",status:1},rules:{title:[{required:!0,message:"请输入WiFi标题",trigger:"blur"},{min:1,max:100,message:"长度在 1 到 100 个字符",trigger:"blur"}],name:[{required:!0,message:"请输入WiFi名称",trigger:"blur"},{min:1,max:100,message:"长度在 1 到 100 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入WiFi密码",trigger:"blur"},{min:1,max:100,message:"长度在 1 到 100 个字符",trigger:"blur"}],merchant_name:[{required:!0,message:"请输入商户名称",trigger:"blur"},{min:1,max:100,message:"长度在 1 到 100 个字符",trigger:"blur"}]},isEdit:!1,wifiId:null,loading:!1}},created(){if(this.$route.params.id){this.isEdit=!0;const t=this.$route.params.id;if(!t||isNaN(t))return this.$message.error("无效的WiFi ID"),void this.goBack();this.wifiId=parseInt(t),this.getDetail()}},methods:{getDetail(){if(!this.wifiId||isNaN(this.wifiId))return this.$message.error("无效的WiFi ID"),void this.goBack();this.loading=!0,Object(s["c"])(this.wifiId).then(t=>{this.form=t.data}).catch(t=>{console.error("获取WiFi码详情失败:",t),this.$message.error("获取WiFi码详情失败: "+(t.message||"未知错误"))}).finally(()=>{this.loading=!1})},submitForm(){this.$refs.form.validate(t=>{if(!t)return!1;{this.loading=!0;const t={...this.form,status:parseInt(this.form.status)};console.log("提交数据:",t),this.isEdit?Object(s["f"])(this.wifiId,t).then(t=>{console.log("更新响应:",t),this.$message.success("更新成功"),this.goBack()}).catch(t=>{console.error("更新失败:",t),this.$message.error("更新失败: "+(t.message||"未知错误"))}).finally(()=>{this.loading=!1}):Object(s["a"])(t).then(t=>{console.log("创建响应:",t),this.$message.success("创建成功"),this.goBack()}).catch(t=>{console.error("创建失败:",t),this.$message.error("创建失败: "+(t.message||"未知错误"))}).finally(()=>{this.loading=!1})}})},cancel(){this.goBack()},goBack(){this.$router.push("/wifi/list")}}},o=n,c=(i("cd65"),i("2877")),u=Object(c["a"])(o,r,a,!1,null,"5bf57e6e",null);e["default"]=u.exports},d251:function(t,e,i){"use strict";i.d(e,"d",(function(){return l})),i.d(e,"c",(function(){return d})),i.d(e,"a",(function(){return m})),i.d(e,"f",(function(){return f})),i.d(e,"b",(function(){return h})),i.d(e,"g",(function(){return g})),i.d(e,"e",(function(){return p}));i("14d9"),i("13d5"),i("e9f5"),i("910d"),i("f665"),i("ab43"),i("9485");var r=i("b775");const a=!1,s="wifi_admin_wifi_list",n=[{id:1,title:"WiFi测试1",name:"Test WiFi",password:"12345678",merchant_name:"测试商家1",qrcode:"",use_count:123,user_id:1,status:1,created_at:"2023-06-01 12:30:45"},{id:2,title:"WiFi测试2",name:"Office WiFi",password:"87654321",merchant_name:"测试商家2",qrcode:"",use_count:456,user_id:2,status:1,created_at:"2023-06-02 10:20:30"}];function o(){try{const t=localStorage.getItem(s);return t?JSON.parse(t):n}catch(t){return console.warn("读取WiFi数据失败，使用默认数据:",t),n}}function c(t){try{localStorage.setItem(s,JSON.stringify(t))}catch(e){console.error("保存WiFi数据失败:",e)}}function u(){const t=new Date,e=t.getFullYear(),i=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0"),a=String(t.getHours()).padStart(2,"0"),s=String(t.getMinutes()).padStart(2,"0"),n=String(t.getSeconds()).padStart(2,"0");return`${e}-${i}-${r} ${a}:${s}:${n}`}function l(t){return a?new Promise(e=>{setTimeout(()=>{const i=o(),r=parseInt(t.page)||1,a=parseInt(t.limit)||10,s=(r-1)*a,n=s+a;let c=i;if(t.keyword){const e=t.keyword.toLowerCase();c=i.filter(t=>t.title.toLowerCase().includes(e)||t.name.toLowerCase().includes(e)||t.merchant_name.toLowerCase().includes(e))}void 0!==t.status&&""!==t.status&&(c=c.filter(e=>e.status===parseInt(t.status)));const u=c.slice(s,n);e({code:200,data:{list:u,total:c.length},message:"获取WiFi列表成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/wifi/list",method:"get",params:t})}function d(t){return a?new Promise(e=>{setTimeout(()=>{const i=o(),r=i.find(e=>e.id===parseInt(t));e(r?{code:200,data:r,message:"获取WiFi详情成功"}:{code:404,message:"未找到该WiFi码"})},300)}):(console.log("请求WiFi详情, ID:",t),Object(r["a"])({url:"/api/v1/admin/wifi/detail/"+t,method:"get"}))}function m(t){return a?new Promise(e=>{setTimeout(()=>{const i=o(),r=i.length>0?Math.max(...i.map(t=>t.id))+1:1,a={id:r,title:t.title,name:t.name,password:t.password,merchant_name:t.merchant_name,qrcode:"",use_count:0,user_id:1,status:t.status,created_at:u()};i.push(a),c(i),console.log("模拟数据 - 创建WiFi码成功:",a),console.log("当前WiFi列表:",i),e({code:200,data:{id:r},message:"创建WiFi码成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/wifi/create",method:"post",data:t})}function f(t,e){return a?new Promise(i=>{setTimeout(()=>{const r=o(),a=r.findIndex(e=>e.id===parseInt(t));-1!==a?(r[a]={...r[a],...e},c(r),console.log("模拟数据 - 更新WiFi码成功:",r[a]),i({code:200,data:{},message:"更新WiFi码成功"})):i({code:404,message:"未找到该WiFi码"})},300)}):Object(r["a"])({url:"/api/v1/admin/wifi/update/"+t,method:"put",data:e})}function h(t){return a?new Promise(e=>{setTimeout(()=>{const i=o(),r=i.findIndex(e=>e.id===parseInt(t));-1!==r?(i.splice(r,1),c(i),console.log("模拟数据 - 删除WiFi码成功, ID:",t),console.log("当前WiFi列表:",i),e({code:200,data:{},message:"删除WiFi码成功"})):e({code:404,message:"未找到该WiFi码"})},300)}):Object(r["a"])({url:"/api/v1/admin/wifi/"+t,method:"delete"})}function g(t,e){return a?new Promise(i=>{setTimeout(()=>{const r=o(),a=r.findIndex(e=>e.id===parseInt(t));-1!==a?(r[a].status=e.status,c(r),console.log("模拟数据 - 更新WiFi码状态成功:",r[a]),i({code:200,data:{},message:"更新WiFi码状态成功"})):i({code:404,message:"未找到该WiFi码"})},300)}):Object(r["a"])({url:"/api/v1/admin/wifi/status/"+t,method:"put",data:e})}function p(t){return a?new Promise(e=>{setTimeout(()=>{const i=o(),r=i.length,a=i.filter(t=>1===t.status).length,s=r-a,n=i.reduce((t,e)=>t+e.use_count,0),c=[],u=new Date;let l=7;t&&t.time_range&&("month"===t.time_range?l=30:"year"===t.time_range&&(l=365));for(let t=l-1;t>=0;t--){const e=new Date(u);e.setDate(e.getDate()-t);const i=`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`;c.push({date:i,count:Math.floor(100*Math.random())+10})}const d=[...i].sort((t,e)=>e.use_count-t.use_count).slice(0,5);e({code:200,data:{stats:{total:r,active:a,inactive:s,total_use_count:n},trend_data:c,top_wifi_list:d},message:"获取WiFi统计数据成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/wifi/stats",method:"get",params:t})}},d4cc:function(t,e,i){},d58f:function(t,e,i){"use strict";var r=i("59ed"),a=i("7b0b"),s=i("44ad"),n=i("07fa"),o=TypeError,c="Reduce of empty array with no initial value",u=function(t){return function(e,i,u,l){var d=a(e),m=s(d),f=n(d);if(r(i),0===f&&u<2)throw new o(c);var h=t?f-1:0,g=t?-1:1;if(u<2)while(1){if(h in m){l=m[h],h+=g;break}if(h+=g,t?h<0:f<=h)throw new o(c)}for(;t?h>=0:f>h;h+=g)h in m&&(l=i(l,m[h],h,d));return l}};t.exports={left:u(!1),right:u(!0)}},f665:function(t,e,i){"use strict";var r=i("23e7"),a=i("c65b"),s=i("2266"),n=i("59ed"),o=i("825a"),c=i("46c4"),u=i("2a62"),l=i("f99f"),d=l("find",TypeError);r({target:"Iterator",proto:!0,real:!0,forced:d},{find:function(t){o(this);try{n(t)}catch(r){u(this,"throw",r)}if(d)return a(d,this,t);var e=c(this),i=0;return s(e,(function(e,r){if(t(e,i++))return r(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})}}]);