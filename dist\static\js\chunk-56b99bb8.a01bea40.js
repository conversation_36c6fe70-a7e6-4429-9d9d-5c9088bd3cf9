(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-56b99bb8"],{"0867":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{staticClass:"filter-container"},[e("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.queryParams}},[e("el-form-item",{attrs:{label:"广告位名称"}},[e("el-input",{attrs:{placeholder:"广告位名称",clearable:""},model:{value:t.queryParams.name,callback:function(e){t.$set(t.queryParams,"name",e)},expression:"queryParams.name"}})],1),e("el-form-item",{attrs:{label:"状态"}},[e("el-select",{attrs:{placeholder:"状态",clearable:""},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},[e("el-option",{attrs:{label:"启用",value:1}}),e("el-option",{attrs:{label:"禁用",value:0}})],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.handleQuery}},[t._v("查询")]),e("el-button",{on:{click:t.resetQuery}},[t._v("重置")]),e("el-button",{attrs:{type:"primary"},on:{click:t.handleCreate}},[t._v("添加广告位")])],1)],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.spaceList,border:""}},[e("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),e("el-table-column",{attrs:{prop:"name",label:"广告位名称",width:"150"}}),e("el-table-column",{attrs:{prop:"code",label:"广告位编码",width:"150"}}),e("el-table-column",{attrs:{label:"尺寸",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.width)+" x "+t._s(e.row.height)+" ")]}}])}),e("el-table-column",{attrs:{prop:"price",label:"价格/天",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" ¥ "+t._s(e.row.price.toFixed(2))+" ")]}}])}),e("el-table-column",{attrs:{prop:"description",label:"描述"}}),e("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:1===a.row.status?"success":"info"}},[t._v(" "+t._s(1===a.row.status?"启用":"禁用")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"180"}}),e("el-table-column",{attrs:{label:"操作",width:"220",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handleUpdate(a.row)}}},[t._v("编辑")]),e("el-button",{attrs:{size:"mini",type:1===a.row.status?"warning":"success"},on:{click:function(e){return t.handleChangeStatus(a.row)}}},[t._v(t._s(1===a.row.status?"禁用":"启用"))]),e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.handleDelete(a.row)}}},[t._v("删除")])]}}])})],1),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{"current-page":t.queryParams.page,"page-sizes":[10,20,30,50],"page-size":t.queryParams.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)},r=[],s=(a("14d9"),a("e9f5"),a("ab43"),a("2b3f")),i={name:"AdSpaceList",data(){return{loading:!1,total:0,queryParams:{page:1,limit:10,name:void 0,status:void 0},spaceList:[]}},created(){console.log("广告位列表页面已创建，准备获取数据..."),this.getList()},methods:{getList(){this.loading=!0,Object(s["h"])(this.queryParams).then(t=>{console.log("广告位列表响应:",t),"success"===t.status?(this.spaceList=(t.data.list||[]).map(t=>({...t,price:parseFloat(t.price)||0})),this.total=t.data.pagination&&t.data.pagination.total||t.data.total||0):this.$message.error(t.message||"获取广告位列表失败")}).catch(t=>{console.error("获取广告位列表错误:",t),this.$message.error("获取广告位列表失败")}).finally(()=>{this.loading=!1})},handleQuery(){this.queryParams.page=1,this.getList()},resetQuery(){this.queryParams={page:1,limit:10,name:void 0,status:void 0},this.getList()},handleSizeChange(t){this.queryParams.limit=t,this.getList()},handleCurrentChange(t){this.queryParams.page=t,this.getList()},handleCreate(){this.$router.push("/ad/space/create")},handleUpdate(t){this.$router.push("/ad/space/edit/"+t.id)},handleChangeStatus(t){const e=1===t.status?0:1,a=1===e?"启用":"禁用";this.$confirm(`确认要${a}该广告位吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(s["l"])(t.id,e).then(()=>{this.$message.success(a+"成功"),this.getList()}).catch(()=>{this.$message.error(a+"失败")})}).catch(()=>{})},handleDelete(t){this.$confirm("确认要删除该广告位吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(s["d"])(t.id).then(()=>{this.$message.success("删除成功"),this.getList()}).catch(()=>{this.$message.error("删除失败")})}).catch(()=>{})}}},o=i,u=(a("3adf"),a("2877")),l=Object(u["a"])(o,n,r,!1,null,"3e25b970",null);e["default"]=l.exports},"2b3f":function(t,e,a){"use strict";a.d(e,"e",(function(){return r})),a.d(e,"h",(function(){return s})),a.d(e,"b",(function(){return i})),a.d(e,"k",(function(){return o})),a.d(e,"d",(function(){return u})),a.d(e,"l",(function(){return l})),a.d(e,"g",(function(){return c})),a.d(e,"a",(function(){return d})),a.d(e,"i",(function(){return p})),a.d(e,"c",(function(){return m})),a.d(e,"j",(function(){return h})),a.d(e,"f",(function(){return f}));var n=a("b775");function r(t){return t?t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:"/uploads/"+t:"/uploads/default-ad.jpg"}function s(t){return Object(n["a"])({url:"/api/v1/admin/advertisement/spaces",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/api/v1/admin/advertisement/spaces",method:"post",data:t})}function o(t,e){return Object(n["a"])({url:"/api/v1/admin/advertisement/spaces/"+t,method:"put",data:e})}function u(t){return Object(n["a"])({url:"/api/v1/admin/advertisement/spaces/"+t,method:"delete"})}function l(t,e){return Object(n["a"])({url:"/api/v1/admin/advertisement/spaces/"+t,method:"put",data:{status:e}})}function c(t){return Object(n["a"])({url:"/api/v1/admin/advertisement/contents",method:"get",params:t})}function d(t){return Object(n["a"])({url:"/api/v1/admin/advertisement/contents",method:"post",data:t})}function p(t,e){return Object(n["a"])({url:"/api/v1/admin/advertisement/contents/"+t,method:"put",data:e})}function m(t){return Object(n["a"])({url:"/api/v1/admin/advertisement/contents/"+t,method:"delete"})}function h(t,e){const a=e&&void 0!==e.status?e.status:0;return Object(n["a"])({url:"/api/v1/admin/advertisement/contents/"+t,method:"put",data:{status:a}})}function f(t){return Object(n["a"])({url:"/api/v1/admin/advertisement/contents/"+t,method:"get"})}},"3adf":function(t,e,a){"use strict";a("a1ef")},a1ef:function(t,e,a){},ab43:function(t,e,a){"use strict";var n=a("23e7"),r=a("c65b"),s=a("59ed"),i=a("825a"),o=a("46c4"),u=a("c5cc"),l=a("9bdd"),c=a("2a62"),d=a("2baa"),p=a("f99f"),m=a("c430"),h=!m&&!d("map",(function(){})),f=!m&&!h&&p("map",TypeError),b=m||h||f,g=u((function(){var t=this.iterator,e=i(r(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));n({target:"Iterator",proto:!0,real:!0,forced:b},{map:function(t){i(this);try{s(t)}catch(e){c(this,"throw",e)}return f?r(f,this,t):new g(o(this),{mapper:t})}})}}]);