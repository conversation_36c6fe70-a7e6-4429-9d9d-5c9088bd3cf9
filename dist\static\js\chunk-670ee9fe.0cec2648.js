(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-670ee9fe"],{"5bb8":function(e,t,a){},8593:function(e,t,a){"use strict";a.d(t,"j",(function(){return d})),a.d(t,"m",(function(){return p})),a.d(t,"i",(function(){return h})),a.d(t,"b",(function(){return b})),a.d(t,"l",(function(){return y})),a.d(t,"d",(function(){return v})),a.d(t,"e",(function(){return _})),a.d(t,"a",(function(){return F})),a.d(t,"k",(function(){return w})),a.d(t,"c",(function(){return I})),a.d(t,"h",(function(){return x})),a.d(t,"g",(function(){return S})),a.d(t,"f",(function(){return M}));a("d9e2"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732");var s=a("b775");const o=!1,r=[{id:1,name:"超级管理员",code:"super_admin",description:"拥有系统所有权限",permissions:["*"],status:1,created_at:"2024-01-01 00:00:00",updated_at:"2024-01-01 00:00:00"},{id:2,name:"运营管理员",code:"operation_admin",description:"负责日常运营管理",permissions:["wifi:*","user:*","order:*","ad:*"],status:1,created_at:"2024-01-15 10:00:00",updated_at:"2024-12-20 15:00:00"}],i=[{id:1,username:"admin",nickname:"超级管理员",email:"<EMAIL>",phone:"13800138000",avatar:'data:image/svg+xml,%3Csvg width="100" height="100" xmlns="http://www.w3.org/2000/svg"%3E%3Ccircle cx="50" cy="50" r="50" fill="%234A90E2"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="white" font-size="40"%3EA%3C/text%3E%3C/svg%3E',role_id:1,role_name:"超级管理员",status:1,last_login_time:"2025-7-7 14:00:00",last_login_ip:"127.0.0.1",created_at:"2024-01-01 00:00:00",updated_at:"2025-7-7 14:00:00"},{id:2,username:"operation",nickname:"运营小王",email:"<EMAIL>",phone:"***********",avatar:"",role_id:2,role_name:"运营管理员",status:1,last_login_time:"2025-7-7 09:00:00",last_login_ip:"*************",created_at:"2024-01-15 10:00:00",updated_at:"2025-7-7 09:00:00"}];let n=[];try{const e=localStorage.getItem("wifi_admin_accounts");n=e?JSON.parse(e):i,e||localStorage.setItem("wifi_admin_accounts",JSON.stringify(i))}catch(T){console.error("初始化mockAccounts失败:",T),n=i}const l=[{id:1,user_id:1,username:"admin",module:"系统管理",action:"更新系统配置",method:"PUT",url:"/api/v1/admin/system/config/update",ip:"127.0.0.1",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)",params:'{"site_name":"华红WIFI共享商业系统"}',result:"success",created_at:"2025-7-7 15:30:00"},{id:2,user_id:2,username:"operation",module:"WiFi管理",action:"创建WiFi热点",method:"POST",url:"/api/v1/admin/wifi/create",ip:"*************",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)",params:'{"name":"星巴克-朝阳店","password":"starbucks2024"}',result:"success",created_at:"2025-7-7 14:20:00"},{id:3,user_id:3,username:"finance",module:"提现管理",action:"审核提现申请",method:"PUT",url:"/api/v1/admin/withdraw/audit/1",ip:"*************",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)",params:'{"status":1,"remark":"审核通过"}',result:"success",created_at:"2025-7-7 11:00:00"}];function c(e){console.log("保存账号数据到localStorage:",e),localStorage.setItem("wifi_admin_accounts",JSON.stringify(e)),localStorage.removeItem("wifi_admin_accounts_processed"),localStorage.removeItem("wifi_admin_accounts_cache")}function m(){try{localStorage.removeItem("wifi_admin_accounts_processed"),localStorage.removeItem("wifi_admin_accounts_cache");const e=localStorage.getItem("wifi_admin_accounts");if(console.log("getStoredAccounts - 从localStorage获取的原始数据:",e),e){const t=JSON.parse(e);return console.log("getStoredAccounts - 解析后的账号数据:",t),t}}catch(T){console.error("getStoredAccounts - 从localStorage获取账号数据失败:",T)}return console.log("getStoredAccounts - 返回默认账号数据"),i}function d(){return o?new Promise(e=>{setTimeout(()=>{const t=getStoredSystemConfig();e({code:200,data:t,message:"获取成功"})},200)}):Object(s["a"])({url:"/api/v1/admin/system/config",method:"get"})}function p(e){return o?new Promise(t=>{setTimeout(()=>{const a=getStoredSystemConfig();e.basic&&Object.assign(a.basic,e.basic),e.payment&&Object.assign(a.payment,e.payment),e.logistics&&Object.assign(a.logistics,e.logistics),e.sms&&(Object.assign(a.sms,e.sms),e.sms.templates&&Object.assign(a.sms.templates,e.sms.templates)),a.updated_at=(new Date).toISOString().replace("T"," ").slice(0,19),saveSystemConfig(a),t({code:200,message:"更新成功"})},500)}):Object(s["a"])({url:"/api/v1/admin/system/config/update",method:"put",data:e})}function u(){const e=localStorage.getItem("wifi_admin_roles");return e?JSON.parse(e):r}function f(e){localStorage.setItem("wifi_admin_roles",JSON.stringify(e))}let g=u();function h(){return new Promise(e=>{setTimeout(()=>{const t=u();g=t,e({code:200,data:{list:t,total:t.length},message:"获取成功"})},200)})}function b(e){return new Promise(t=>{setTimeout(()=>{const a=u();if(a.some(t=>t.name===e.name))return void t({code:400,message:"角色名称已存在"});if(e.code&&a.some(t=>t.code===e.code))return void t({code:400,message:"角色编码已存在"});const s={...e,id:a.length>0?Math.max(...a.map(e=>e.id))+1:1,created_at:(new Date).toISOString().replace("T"," ").slice(0,19),updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)};a.push(s),f(a),g=a,t({code:200,data:s,message:"创建成功"})},500)})}function y(e,t){return new Promise((a,s)=>{setTimeout(()=>{const o=u(),r=o.findIndex(t=>t.id===parseInt(e));if(r>-1){if(1===e&&(t.code!==o[r].code||0===t.status))return void s(new Error("不能修改超级管理员的关键信息"));o[r]={...o[r],...t,updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)},f(o),g=o,a({code:200,data:o[r],message:"更新成功"})}else s(new Error("角色不存在"))},500)})}function v(e){return new Promise((t,a)=>{setTimeout(()=>{if(1===parseInt(e))return void a(new Error("超级管理员角色不能删除"));const s=u(),o=s.findIndex(t=>t.id===parseInt(e));o>-1?(s.splice(o,1),f(s),g=s,t({code:200,message:"删除成功"})):a(new Error("角色不存在"))},300)})}function _(e){return o?new Promise(t=>{setTimeout(()=>{let a=m();console.log("getAccountList获取到的原始账号数据:",a);const s=u();console.log("获取到的角色数据:",s),a=a.map(e=>{const t=s.find(t=>t.id===e.role_id);return{...e,role_name:t?t.name:"未知角色"}}),e&&(e.username&&(a=a.filter(t=>t.username.includes(e.username))),e.nickname&&(a=a.filter(t=>t.nickname&&t.nickname.includes(e.nickname))),e.role_id&&(a=a.filter(t=>t.role_id===parseInt(e.role_id))),void 0!==e.status&&""!==e.status&&(a=a.filter(t=>t.status===parseInt(e.status)))),localStorage.setItem("wifi_admin_accounts_processed",JSON.stringify(a)),console.log("getAccountList处理后的账号数据:",a),t({code:200,data:{total:a.length,list:a},message:"获取成功"})},200)}):Object(s["a"])({url:"/api/v1/admin/system/account/list",method:"get",params:e})}function F(e){return o?new Promise((t,a)=>{setTimeout(()=>{const s=m();if(s.some(t=>t.username===e.username))return void a({code:400,message:"用户名已存在"});const o=u();console.log("创建账号时获取的角色数据:",o);const r=o.find(t=>t.id===parseInt(e.role_id));if(!r)return void a({code:400,message:"角色不存在"});const i={id:s.length>0?Math.max(...s.map(e=>e.id))+1:1,username:e.username,nickname:e.real_name||e.username,real_name:e.real_name||"",email:e.email||"",phone:e.phone||"",avatar:e.avatar||"",role_id:parseInt(e.role_id),role_name:r.name,status:parseInt(e.status)||1,last_login_time:"",last_login_ip:"",created_at:(new Date).toISOString().replace("T"," ").slice(0,19),updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)};s.push(i),c(s),localStorage.removeItem("wifi_admin_accounts_processed"),localStorage.removeItem("wifi_admin_accounts_cache"),t({code:200,data:i,message:"创建成功"})},500)}):Object(s["a"])({url:"/api/v1/admin/system/account/create",method:"post",data:e})}function w(e,t){return o?new Promise((a,s)=>{setTimeout(()=>{const o=m();console.log("更新账号 - 原始账号列表:",o),console.log("更新账号 - ID:",e,"类型:",typeof e),console.log("更新账号 - 数据:",t);const r=parseInt(e);o.forEach(e=>{console.log("账号ID:",e.id,"类型:",typeof e.id)});const i=o.findIndex(e=>parseInt(e.id)===r);if(console.log("找到的账号索引:",i),-1===i)return console.error("账号不存在, ID:",e),void s({code:404,message:"账号不存在"});if(t.username&&t.username!==o[i].username&&o.some(e=>parseInt(e.id)!==r&&e.username===t.username))return void s({code:400,message:"用户名已存在"});const n=u(),l=n.find(e=>e.id===parseInt(t.role_id));if(!l)return void s({code:400,message:"角色不存在"});const d={...o[i],...t,role_id:parseInt(t.role_id),role_name:l.name,updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)};o[i]=d,console.log("更新后的账号:",d),console.log("更新后的账号列表:",o),c(o),a({code:200,data:d,message:"更新成功"})},500)}):Object(s["a"])({url:"/api/v1/admin/system/account/update/"+e,method:"put",data:t})}function I(e){return o?new Promise((t,a)=>{setTimeout(()=>{console.log("删除账号 - ID:",e,"类型:",typeof e);const s=parseInt(e);if(1===s)return console.error("尝试删除超级管理员账号"),void a(new Error("超级管理员账号不能删除"));const o=m();console.log("删除账号 - 原始账号列表:",o),o.forEach(e=>{console.log("账号ID:",e.id,"类型:",typeof e.id)});const r=o.findIndex(e=>parseInt(e.id)===s);if(console.log("找到的账号索引:",r),r>-1){const e=o[r];console.log("要删除的账号:",e),o.splice(r,1),console.log("删除后的账号列表:",o),c(o),t({code:200,message:"删除成功"})}else console.error("账号不存在, ID:",e),a(new Error("账号不存在"))},300)}):Object(s["a"])({url:"/api/v1/admin/system/account/delete/"+e,method:"delete"})}function x(e){return o?new Promise(t=>{setTimeout(()=>{const a=parseInt(e.page)||1,s=parseInt(e.limit)||10,o=[...l];for(let e=4;e<=20;e++)o.push({id:e,user_id:Math.floor(4*Math.random())+1,username:["admin","operation","finance","service01"][Math.floor(4*Math.random())],module:["系统管理","WiFi管理","用户管理","订单管理"][Math.floor(4*Math.random())],action:["查看列表","创建记录","更新记录","删除记录"][Math.floor(4*Math.random())],method:["GET","POST","PUT","DELETE"][Math.floor(4*Math.random())],url:"/api/v1/admin/xxx",ip:"192.168.1."+(100+Math.floor(10*Math.random())),user_agent:"Mozilla/5.0",params:"{}",result:Math.random()>.1?"success":"error",created_at:new Date(Date.now()-36e5*e).toISOString().replace("T"," ").slice(0,19)});o.sort((e,t)=>new Date(t.created_at)-new Date(e.created_at));const r=o.length,i=(a-1)*s,n=i+s,c=o.slice(i,n);t({code:200,data:{list:c,total:r,page:a,limit:s},message:"获取成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/log/operation",method:"get",params:e})}function S(e){return o?new Promise(t=>{setTimeout(()=>{const a=parseInt(e.page)||1,s=parseInt(e.limit)||10,o=[];for(let e=1;e<=30;e++)o.push({id:e,user_id:Math.floor(4*Math.random())+1,username:["admin","operation","finance","service01"][Math.floor(4*Math.random())],ip:"192.168.1."+(100+Math.floor(20*Math.random())),location:["北京市","上海市","广州市","深圳市"][Math.floor(4*Math.random())],browser:["Chrome","Firefox","Safari","Edge"][Math.floor(4*Math.random())],os:["Windows 10","macOS","Ubuntu","Windows 11"][Math.floor(4*Math.random())],status:Math.random()>.2?1:0,message:Math.random()>.2?"登录成功":"密码错误",created_at:new Date(Date.now()-72e5*e).toISOString().replace("T"," ").slice(0,19)});const r=o.length,i=(a-1)*s,n=i+s,l=o.slice(i,n);t({code:200,data:{list:l,total:r,page:a,limit:s},message:"获取成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/log/login",method:"get",params:e})}function M(e){return o?new Promise(t=>{setTimeout(()=>{const a=parseInt(e.page)||1,s=parseInt(e.limit)||10,o=[];for(let e=1;e<=15;e++){const t=Math.random()>.3,a=["Connection timeout","Invalid parameter","File not found","Permission denied","Database connection failed","Redis connection refused","API rate limit exceeded","Memory allocation failed"],s=a[Math.floor(Math.random()*a.length)],r=t?`Error: ${s}\n    at Object.<anonymous> (/app/src/api/wifi.js:45:15)\n    at Module._compile (internal/modules/cjs/loader.js:1063:30)\n    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)\n    at Module.load (internal/modules/cjs/loader.js:928:32)\n    at Function.Module._load (internal/modules/cjs/loader.js:769:14)\n    at Module.require (internal/modules/cjs/loader.js:952:19)\n    at require (internal/modules/cjs/helpers.js:88:18)\n    at Object.<anonymous> (/app/src/router/index.js:12:18)\n    at Module._compile (internal/modules/cjs/loader.js:1063:30)\n    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)`:null;o.push({id:e,level:["error","warning"][Math.floor(2*Math.random())],module:["API","Database","Redis","File"][Math.floor(4*Math.random())],message:s,stack:r,user_id:Math.floor(4*Math.random())+1,username:["admin","operation","finance","service01"][Math.floor(4*Math.random())],ip:"192.168.1."+(100+Math.floor(20*Math.random())),url:"/api/v1/admin/xxx",created_at:new Date(Date.now()-108e5*e).toISOString().replace("T"," ").slice(0,19)})}const r=o.length,i=(a-1)*s,n=i+s,l=o.slice(i,n);t({code:200,data:{list:l,total:r,page:a,limit:s},message:"获取成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/log/error",method:"get",params:e})}},"97d1":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-container"},[t("el-tabs",{model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[t("el-tab-pane",{attrs:{label:"基础设置",name:"basic"}},[t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("系统基础设置")])]),t("el-form",{ref:"basicForm",attrs:{model:e.basicForm,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"系统名称"}},[t("el-input",{attrs:{placeholder:"请输入系统名称"},model:{value:e.basicForm.siteName,callback:function(t){e.$set(e.basicForm,"siteName",t)},expression:"basicForm.siteName"}})],1),t("el-form-item",{attrs:{label:"系统Logo"}},[t("el-upload",{staticClass:"logo-uploader",attrs:{action:"#","show-file-list":!1,"http-request":e.uploadLogo,"before-upload":e.beforeLogoUpload}},[e.basicForm.siteLogo?t("img",{staticClass:"logo",attrs:{src:e.basicForm.siteLogo}}):t("i",{staticClass:"el-icon-plus logo-uploader-icon"})]),t("div",{staticClass:"tip"},[e._v("建议尺寸：200px * 60px")])],1),t("el-form-item",{attrs:{label:"版权信息"}},[t("el-input",{attrs:{placeholder:"请输入版权信息"},model:{value:e.basicForm.copyright,callback:function(t){e.$set(e.basicForm,"copyright",t)},expression:"basicForm.copyright"}})],1),t("el-form-item",{attrs:{label:"备案号"}},[t("el-input",{attrs:{placeholder:"请输入备案号"},model:{value:e.basicForm.icp,callback:function(t){e.$set(e.basicForm,"icp",t)},expression:"basicForm.icp"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.saveBasicConfig}},[e._v("保存设置")])],1)],1)],1)],1),t("el-tab-pane",{attrs:{label:"支付设置",name:"payment"}},[t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("支付方式配置")])]),t("el-form",{ref:"paymentForm",attrs:{model:e.paymentForm,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"微信支付"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},model:{value:e.paymentForm.enableWechat,callback:function(t){e.$set(e.paymentForm,"enableWechat",t)},expression:"paymentForm.enableWechat"}})],1),1===e.paymentForm.enableWechat?t("el-form-item",{attrs:{label:"商户ID"}},[t("el-input",{attrs:{placeholder:"请输入微信支付商户ID"},model:{value:e.paymentForm.wechatMchId,callback:function(t){e.$set(e.paymentForm,"wechatMchId",t)},expression:"paymentForm.wechatMchId"}})],1):e._e(),1===e.paymentForm.enableWechat?t("el-form-item",{attrs:{label:"AppID"}},[t("el-input",{attrs:{placeholder:"请输入微信支付AppID"},model:{value:e.paymentForm.wechatAppId,callback:function(t){e.$set(e.paymentForm,"wechatAppId",t)},expression:"paymentForm.wechatAppId"}})],1):e._e(),1===e.paymentForm.enableWechat?t("el-form-item",{attrs:{label:"API密钥"}},[t("el-input",{attrs:{placeholder:"请输入微信支付API密钥","show-password":""},model:{value:e.paymentForm.wechatApiKey,callback:function(t){e.$set(e.paymentForm,"wechatApiKey",t)},expression:"paymentForm.wechatApiKey"}})],1):e._e(),t("el-divider",{attrs:{"content-position":"left"}},[e._v("支付宝支付")]),t("el-form-item",{attrs:{label:"支付宝支付"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},model:{value:e.paymentForm.enableAlipay,callback:function(t){e.$set(e.paymentForm,"enableAlipay",t)},expression:"paymentForm.enableAlipay"}})],1),1===e.paymentForm.enableAlipay?t("el-form-item",{attrs:{label:"AppID"}},[t("el-input",{attrs:{placeholder:"请输入支付宝AppID"},model:{value:e.paymentForm.alipayAppId,callback:function(t){e.$set(e.paymentForm,"alipayAppId",t)},expression:"paymentForm.alipayAppId"}})],1):e._e(),1===e.paymentForm.enableAlipay?t("el-form-item",{attrs:{label:"公钥"}},[t("el-input",{attrs:{placeholder:"请输入支付宝公钥",type:"textarea",rows:"3"},model:{value:e.paymentForm.alipayPublicKey,callback:function(t){e.$set(e.paymentForm,"alipayPublicKey",t)},expression:"paymentForm.alipayPublicKey"}})],1):e._e(),1===e.paymentForm.enableAlipay?t("el-form-item",{attrs:{label:"私钥"}},[t("el-input",{attrs:{placeholder:"请输入支付宝私钥",type:"textarea",rows:"3","show-password":""},model:{value:e.paymentForm.alipayPrivateKey,callback:function(t){e.$set(e.paymentForm,"alipayPrivateKey",t)},expression:"paymentForm.alipayPrivateKey"}})],1):e._e(),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.savePaymentConfig}},[e._v("保存设置")])],1)],1)],1)],1),t("el-tab-pane",{attrs:{label:"物流设置",name:"logistics"}},[t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("物流配置")])]),t("el-form",{ref:"logisticsForm",attrs:{model:e.logisticsForm,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"物流查询接口"}},[t("el-select",{attrs:{placeholder:"请选择物流查询接口"},model:{value:e.logisticsForm.apiProvider,callback:function(t){e.$set(e.logisticsForm,"apiProvider",t)},expression:"logisticsForm.apiProvider"}},[t("el-option",{attrs:{label:"快递鸟",value:"kdniao"}}),t("el-option",{attrs:{label:"快递100",value:"kd100"}}),t("el-option",{attrs:{label:"阿里云物流",value:"aliyun"}})],1)],1),t("el-form-item",{attrs:{label:"接口AppID"}},[t("el-input",{attrs:{placeholder:"请输入接口AppID"},model:{value:e.logisticsForm.appId,callback:function(t){e.$set(e.logisticsForm,"appId",t)},expression:"logisticsForm.appId"}})],1),t("el-form-item",{attrs:{label:"接口AppKey"}},[t("el-input",{attrs:{placeholder:"请输入接口AppKey","show-password":""},model:{value:e.logisticsForm.appKey,callback:function(t){e.$set(e.logisticsForm,"appKey",t)},expression:"logisticsForm.appKey"}})],1),t("el-form-item",{attrs:{label:"运费设置"}},[t("el-radio-group",{model:{value:e.logisticsForm.feeType,callback:function(t){e.$set(e.logisticsForm,"feeType",t)},expression:"logisticsForm.feeType"}},[t("el-radio",{attrs:{label:1}},[e._v("统一运费")]),t("el-radio",{attrs:{label:2}},[e._v("按地区设置")]),t("el-radio",{attrs:{label:3}},[e._v("包邮")])],1)],1),1===e.logisticsForm.feeType?t("el-form-item",{attrs:{label:"默认运费"}},[t("el-input-number",{attrs:{min:0,precision:2,step:.5},model:{value:e.logisticsForm.defaultFee,callback:function(t){e.$set(e.logisticsForm,"defaultFee",t)},expression:"logisticsForm.defaultFee"}})],1):e._e(),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.saveLogisticsConfig}},[e._v("保存设置")])],1)],1)],1)],1),t("el-tab-pane",{attrs:{label:"短信配置",name:"sms"}},[t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("短信配置")])]),t("el-form",{ref:"smsForm",attrs:{model:e.smsForm,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"短信服务商"}},[t("el-select",{attrs:{placeholder:"请选择短信服务商"},model:{value:e.smsForm.provider,callback:function(t){e.$set(e.smsForm,"provider",t)},expression:"smsForm.provider"}},[t("el-option",{attrs:{label:"阿里云短信",value:"aliyun"}}),t("el-option",{attrs:{label:"腾讯云短信",value:"tencent"}})],1)],1),t("el-form-item",{attrs:{label:"AccessKeyId"}},[t("el-input",{attrs:{placeholder:"请输入AccessKeyId"},model:{value:e.smsForm.accessKeyId,callback:function(t){e.$set(e.smsForm,"accessKeyId",t)},expression:"smsForm.accessKeyId"}})],1),t("el-form-item",{attrs:{label:"AccessKeySecret"}},[t("el-input",{attrs:{placeholder:"请输入AccessKeySecret","show-password":""},model:{value:e.smsForm.accessKeySecret,callback:function(t){e.$set(e.smsForm,"accessKeySecret",t)},expression:"smsForm.accessKeySecret"}})],1),t("el-form-item",{attrs:{label:"短信签名"}},[t("el-input",{attrs:{placeholder:"请输入短信签名"},model:{value:e.smsForm.signName,callback:function(t){e.$set(e.smsForm,"signName",t)},expression:"smsForm.signName"}})],1),t("el-divider",{attrs:{"content-position":"left"}},[e._v("短信模板配置")]),t("el-form-item",{attrs:{label:"验证码模板ID"}},[t("el-input",{attrs:{placeholder:"请输入验证码短信模板ID"},model:{value:e.smsForm.templates.verifyCode,callback:function(t){e.$set(e.smsForm.templates,"verifyCode",t)},expression:"smsForm.templates.verifyCode"}})],1),t("el-form-item",{attrs:{label:"订单通知模板ID"}},[t("el-input",{attrs:{placeholder:"请输入订单通知短信模板ID"},model:{value:e.smsForm.templates.orderNotify,callback:function(t){e.$set(e.smsForm.templates,"orderNotify",t)},expression:"smsForm.templates.orderNotify"}})],1),t("el-form-item",{attrs:{label:"发货通知模板ID"}},[t("el-input",{attrs:{placeholder:"请输入发货通知短信模板ID"},model:{value:e.smsForm.templates.deliveryNotify,callback:function(t){e.$set(e.smsForm.templates,"deliveryNotify",t)},expression:"smsForm.templates.deliveryNotify"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.saveSmsConfig}},[e._v("保存设置")])],1)],1)],1)],1)],1)],1)},o=[],r=a("8593"),i={name:"SystemConfig",data(){return{activeTab:"basic",loading:!1,basicForm:{siteName:"WiFi共享商业管理系统",siteLogo:"",copyright:"© 2023 WiFi共享商业管理系统",icp:""},paymentForm:{enableWechat:0,wechatMchId:"",wechatAppId:"",wechatApiKey:"",enableAlipay:0,alipayAppId:"",alipayPublicKey:"",alipayPrivateKey:""},logisticsForm:{apiProvider:"kdniao",appId:"",appKey:"",feeType:1,defaultFee:10},smsForm:{provider:"aliyun",accessKeyId:"",accessKeySecret:"",signName:"",templates:{verifyCode:"",orderNotify:"",deliveryNotify:""}}}},created(){this.getConfig()},methods:{getConfig(){this.loading=!0,Object(r["j"])().then(e=>{const t=e.data||{};t.basic&&(this.basicForm={...this.basicForm,...t.basic}),t.payment&&(this.paymentForm={...this.paymentForm,...t.payment}),t.logistics&&(this.logisticsForm={...this.logisticsForm,...t.logistics}),t.sms&&(this.smsForm={...this.smsForm,...t.sms},this.smsForm.templates||(this.smsForm.templates={verifyCode:"",orderNotify:"",deliveryNotify:""})),this.loading=!1}).catch(()=>{this.$message.error("获取配置失败"),this.loading=!1})},beforeLogoUpload(e){const t=e.type.startsWith("image/"),a=e.size/1024/1024<2;return t?!!a||(this.$message.error("上传Logo大小不能超过 2MB!"),!1):(this.$message.error("上传Logo只能是图片格式!"),!1)},uploadLogo(e){const{file:t}=e,a=new FileReader;a.readAsDataURL(t),a.onload=()=>{this.basicForm.siteLogo=a.result,this.$message.success("Logo上传成功")},a.onerror=()=>{this.$message.error("Logo上传失败")}},saveBasicConfig(){this.loading=!0;const e={basic:this.basicForm};Object(r["m"])(e).then(e=>{200===e.code?this.$message.success("基础设置保存成功"):this.$message.error(e.message||"保存失败")}).catch(()=>{this.$message.error("保存失败")}).finally(()=>{this.loading=!1})},savePaymentConfig(){this.loading=!0;const e={payment:this.paymentForm};Object(r["m"])(e).then(e=>{200===e.code?this.$message.success("支付设置保存成功"):this.$message.error(e.message||"保存失败")}).catch(()=>{this.$message.error("保存失败")}).finally(()=>{this.loading=!1})},saveLogisticsConfig(){this.loading=!0;const e={logistics:this.logisticsForm};Object(r["m"])(e).then(e=>{200===e.code?this.$message.success("物流设置保存成功"):this.$message.error(e.message||"保存失败")}).catch(()=>{this.$message.error("保存失败")}).finally(()=>{this.loading=!1})},saveSmsConfig(){this.loading=!0;const e={sms:this.smsForm};Object(r["m"])(e).then(e=>{200===e.code?this.$message.success("短信配置保存成功"):this.$message.error(e.message||"保存失败")}).catch(()=>{this.$message.error("保存失败")}).finally(()=>{this.loading=!1})}}},n=i,l=(a("f8de"),a("2877")),c=Object(l["a"])(n,s,o,!1,null,"33e21db5",null);t["default"]=c.exports},a732:function(e,t,a){"use strict";var s=a("23e7"),o=a("c65b"),r=a("2266"),i=a("59ed"),n=a("825a"),l=a("46c4"),c=a("2a62"),m=a("f99f"),d=m("some",TypeError);s({target:"Iterator",proto:!0,real:!0,forced:d},{some:function(e){n(this);try{i(e)}catch(s){c(this,"throw",s)}if(d)return o(d,this,e);var t=l(this),a=0;return r(t,(function(t,s){if(e(t,a++))return s()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},ab43:function(e,t,a){"use strict";var s=a("23e7"),o=a("c65b"),r=a("59ed"),i=a("825a"),n=a("46c4"),l=a("c5cc"),c=a("9bdd"),m=a("2a62"),d=a("2baa"),p=a("f99f"),u=a("c430"),f=!u&&!d("map",(function(){})),g=!u&&!f&&p("map",TypeError),h=u||f||g,b=l((function(){var e=this.iterator,t=i(o(this.next,e)),a=this.done=!!t.done;if(!a)return c(e,this.mapper,[t.value,this.counter++],!0)}));s({target:"Iterator",proto:!0,real:!0,forced:h},{map:function(e){i(this);try{r(e)}catch(t){m(this,"throw",t)}return g?o(g,this,e):new b(n(this),{mapper:e})}})},f665:function(e,t,a){"use strict";var s=a("23e7"),o=a("c65b"),r=a("2266"),i=a("59ed"),n=a("825a"),l=a("46c4"),c=a("2a62"),m=a("f99f"),d=m("find",TypeError);s({target:"Iterator",proto:!0,real:!0,forced:d},{find:function(e){n(this);try{i(e)}catch(s){c(this,"throw",s)}if(d)return o(d,this,e);var t=l(this),a=0;return r(t,(function(t,s){if(e(t,a++))return s(t)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},f8de:function(e,t,a){"use strict";a("5bb8")}}]);