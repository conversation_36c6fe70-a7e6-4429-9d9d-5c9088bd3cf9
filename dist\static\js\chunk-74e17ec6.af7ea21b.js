(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-74e17ec6"],{"330e":function(t,a,e){"use strict";e("34b0")},"333d":function(t,a,e){"use strict";var r=function(){var t=this,a=t._self._c;return a("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[a("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(a){t.currentPage=a},"update:current-page":function(a){t.currentPage=a},"update:pageSize":function(a){t.pageSize=a},"update:page-size":function(a){t.pageSize=a},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},i=[],s={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get(){return this.page},set(t){this.$emit("update:page",t)}},pageSize:{get(){return this.limit},set(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange(t){this.$emit("pagination",{page:this.currentPage,limit:t})},handleCurrentChange(t){this.$emit("pagination",{page:t,limit:this.pageSize})}}},n=s,o=(e("330e"),e("2877")),l=Object(o["a"])(n,r,i,!1,null,"11252b03",null);a["a"]=l.exports},"34b0":function(t,a,e){},5485:function(t,a,e){},"8fbb":function(t,a,e){"use strict";e("5485")},ab43:function(t,a,e){"use strict";var r=e("23e7"),i=e("c65b"),s=e("59ed"),n=e("825a"),o=e("46c4"),l=e("c5cc"),c=e("9bdd"),d=e("2a62"),u=e("2baa"),p=e("f99f"),h=e("c430"),f=!h&&!u("map",(function(){})),m=!h&&!f&&p("map",TypeError),v=h||f||m,g=l((function(){var t=this.iterator,a=n(i(this.next,t)),e=this.done=!!a.done;if(!e)return c(t,this.mapper,[a.value,this.counter++],!0)}));r({target:"Iterator",proto:!0,real:!0,forced:v},{map:function(t){n(this);try{s(t)}catch(a){d(this,"throw",a)}return m?i(m,this,t):new g(o(this),{mapper:t})}})},ca3e:function(t,a,e){"use strict";e.r(a);var r=function(){var t=this,a=t._self._c;return a("div",{staticClass:"platform-revenue"},[a("el-row",{staticClass:"revenue-overview",attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"overview-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"card-icon total"},[a("i",{staticClass:"el-icon-money"})]),a("div",{staticClass:"card-info"},[a("h3",[t._v("总收益")]),a("p",{staticClass:"amount"},[t._v(t._s(t.formatCurrency(t.overview.total_revenue)))])])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"overview-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"card-icon today"},[a("i",{staticClass:"el-icon-calendar"})]),a("div",{staticClass:"card-info"},[a("h3",[t._v("今日收益")]),a("p",{staticClass:"amount"},[t._v(t._s(t.formatCurrency(t.overview.today_revenue)))])])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"overview-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"card-icon platform"},[a("i",{staticClass:"el-icon-office-building"})]),a("div",{staticClass:"card-info"},[a("h3",[t._v("平台收益")]),a("p",{staticClass:"amount"},[t._v(t._s(t.formatCurrency(t.overview.platform_revenue)))])])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"overview-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"card-icon leader"},[a("i",{staticClass:"el-icon-user"})]),a("div",{staticClass:"card-info"},[a("h3",[t._v("团长收益")]),a("p",{staticClass:"amount"},[t._v(t._s(t.formatCurrency(t.overview.leader_revenue)))])])])])],1)],1),a("el-card",{staticClass:"chart-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("收益趋势")]),a("div",{staticStyle:{float:"right"}},[a("el-radio-group",{on:{change:t.loadTrendData},model:{value:t.trendPeriod,callback:function(a){t.trendPeriod=a},expression:"trendPeriod"}},[a("el-radio-button",{attrs:{label:"7d"}},[t._v("近7天")]),a("el-radio-button",{attrs:{label:"30d"}},[t._v("近30天")]),a("el-radio-button",{attrs:{label:"90d"}},[t._v("近90天")])],1)],1)]),a("div",{ref:"trendChart",staticStyle:{height:"400px"}})]),a("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"chart-card"},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[t._v("业务类型分布")])]),a("div",{ref:"businessChart",staticStyle:{height:"300px"}})])],1),a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"chart-card"},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[t._v("收益分配比例")])]),a("div",{ref:"distributionChart",staticStyle:{height:"300px"}})])],1)],1),a("el-card",{staticClass:"table-card",staticStyle:{"margin-top":"20px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("财务报表")]),a("div",{staticStyle:{float:"right"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.loadFinancialData},model:{value:t.dateRange,callback:function(a){t.dateRange=a},expression:"dateRange"}}),a("el-button",{attrs:{type:"primary",icon:"el-icon-download"},on:{click:t.exportReport}},[t._v(" 导出报表 ")])],1)]),a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.financialData,border:""}},[a("el-table-column",{attrs:{prop:"date",label:"日期",width:"120"}}),a("el-table-column",{attrs:{prop:"business_type",label:"业务类型",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:t.getBusinessTypeColor(e.row.business_type)}},[t._v(" "+t._s(t.getBusinessTypeName(e.row.business_type))+" ")])]}}])}),a("el-table-column",{attrs:{prop:"total_amount",label:"总收益",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.formatCurrency(e.row.total_amount)))])]}}])}),a("el-table-column",{attrs:{prop:"platform_amount",label:"平台收益",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.formatCurrency(e.row.platform_amount)))])]}}])}),a("el-table-column",{attrs:{prop:"leader_amount",label:"团长收益",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.formatCurrency(e.row.leader_amount)))])]}}])}),a("el-table-column",{attrs:{prop:"transaction_count",label:"交易笔数",width:"100"}}),a("el-table-column",{attrs:{prop:"avg_amount",label:"平均金额",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.formatCurrency(e.row.avg_amount)))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.financialTotal>0,expression:"financialTotal > 0"}],attrs:{total:t.financialTotal,page:t.financialQuery.page,limit:t.financialQuery.limit},on:{"update:page":function(a){return t.$set(t.financialQuery,"page",a)},"update:limit":function(a){return t.$set(t.financialQuery,"limit",a)},pagination:t.loadFinancialData}})],1)],1)},i=[],s=(e("e9f5"),e("ab43"),e("b775"));function n(){return Object(s["a"])({url:"/api/v1/admin/platform/revenue",method:"get"})}function o(t){return Object(s["a"])({url:"/api/v1/admin/platform/revenue-trend",method:"get",params:t})}function l(t){return Object(s["a"])({url:"/api/v1/admin/platform/business-distribution",method:"get",params:t})}function c(t){return Object(s["a"])({url:"/api/v1/admin/platform/financial-report",method:"get",params:t})}var d=e("333d"),u=e("313e"),p={name:"PlatformRevenue",components:{Pagination:d["a"]},data(){return{overview:{total_revenue:0,today_revenue:0,platform_revenue:0,leader_revenue:0},trendPeriod:"7d",trendChart:null,businessChart:null,distributionChart:null,dateRange:[],financialData:[],financialTotal:0,financialQuery:{page:1,limit:20}}},mounted(){this.loadOverviewData(),this.loadTrendData(),this.loadBusinessData(),this.loadFinancialData(),this.initCharts()},beforeDestroy(){this.trendChart&&this.trendChart.dispose(),this.businessChart&&this.businessChart.dispose(),this.distributionChart&&this.distributionChart.dispose()},methods:{async loadOverviewData(){try{const t=await n();this.overview=t.data}catch(t){console.error("加载收益概览失败:",t)}},async loadTrendData(){try{const t=await o({period:this.trendPeriod});this.renderTrendChart(t.data)}catch(t){console.error("加载收益趋势失败:",t)}},async loadBusinessData(){try{const t=await l();this.renderBusinessChart(t.data),this.renderDistributionChart()}catch(t){console.error("加载业务分布失败:",t)}},async loadFinancialData(){try{const t={...this.financialQuery,start_date:this.dateRange[0],end_date:this.dateRange[1]},a=await c(t);this.financialData=a.data.items,this.financialTotal=a.data.total}catch(t){console.error("加载财务报表失败:",t)}},initCharts(){this.$nextTick(()=>{this.trendChart=u["a"](this.$refs.trendChart),this.businessChart=u["a"](this.$refs.businessChart),this.distributionChart=u["a"](this.$refs.distributionChart)})},renderTrendChart(t){if(!this.trendChart)return;const a={title:{text:"收益趋势图"},tooltip:{trigger:"axis"},legend:{data:["平台收益","团长收益","总收益"]},xAxis:{type:"category",data:t.map(t=>t.date)},yAxis:{type:"value"},series:[{name:"平台收益",type:"line",data:t.map(t=>t.platform_amount)},{name:"团长收益",type:"line",data:t.map(t=>t.leader_amount)},{name:"总收益",type:"line",data:t.map(t=>t.total_amount)}]};this.trendChart.setOption(a)},renderBusinessChart(t){if(!this.businessChart)return;const a={title:{text:"业务类型分布",left:"center"},tooltip:{trigger:"item"},series:[{type:"pie",radius:"50%",data:t.map(t=>({value:t.total_amount,name:this.getBusinessTypeName(t.business_type)}))}]};this.businessChart.setOption(a)},renderDistributionChart(){if(!this.distributionChart)return;const t={title:{text:"收益分配比例",left:"center"},tooltip:{trigger:"item"},series:[{type:"pie",radius:"50%",data:[{value:this.overview.platform_revenue,name:"平台收益"},{value:this.overview.leader_revenue,name:"团长收益"}]}]};this.distributionChart.setOption(t)},formatCurrency(t){return"¥ "+parseFloat(t||0).toFixed(2)},getBusinessTypeName(t){const a={wifi_share:"WiFi分享",goods_sale:"商品销售",advertisement:"广告收益"};return a[t]||t},getBusinessTypeColor(t){const a={wifi_share:"primary",goods_sale:"success",advertisement:"warning"};return a[t]||"info"},exportReport(){this.$message.success("报表导出功能开发中...")}}},h=p,f=(e("8fbb"),e("2877")),m=Object(f["a"])(h,r,i,!1,null,"02d073c0",null);a["default"]=m.exports}}]);