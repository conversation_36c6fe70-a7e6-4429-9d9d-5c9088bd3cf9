import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '',  // 使用环境变量配置的API基础URL
  timeout: 120000,  // 增加超时时间到120秒
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Cache-Control': 'no-cache',  // 禁用缓存
    'Pragma': 'no-cache'
  },
  withCredentials: false  // 生产环境不需要跨域凭证
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 阻止对特定第三方API的请求
    if (config.url && config.url.includes('api.yhchj.com')) {
      console.warn('阻止了对 api.yhchj.com 的请求:', config.url)
      return Promise.reject(new Error('Blocked request to api.yhchj.com'))
    }

    // 调试信息：记录API请求
    console.log(`${process.env.NODE_ENV}环境API请求:`, (config.baseURL || '') + (config.url || ''))
    console.log('Request Config:', {
      url: config.url,
      method: config.method,
      baseURL: config.baseURL,
      fullURL: (config.baseURL || '') + (config.url || '')
    })

    // 发送请求前处理
    if (store.getters.token) {
      config.headers.Authorization = `Bearer ${getToken()}`
    }

    return config
  },
  error => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('Response:', {
        status: response.status,
        headers: response.headers,
        data: response.data
      })
    }

    const res = response.data

    // 如果返回的是二进制数据或其他非JSON格式，直接返回
    if (response.config.responseType === 'blob' || response.config.responseType === 'arraybuffer') {
      return response.data
    }

    // 直接返回后端的原始数据格式，不进行转换
    return res
  },
  error => {
    console.error('Response Error:', {
      error: error,
      response: error.response,
      config: error.config
    })

    let message = error.message
    if (error.response && error.response.data && error.response.data.message) {
      // 优先使用后端返回的错误消息
      message = error.response.data.message
    } else if (error.response) {
      switch (error.response.status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求错误，未找到该资源'
          break
        case 405:
          message = '请求方法不允许'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 501:
          message = '服务未实现'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        case 505:
          message = 'HTTP版本不受支持'
          break
        default:
          message = `连接错误 ${error.response.status}`
      }
    } else if (error.request) {
      message = '网络连接异常，请检查您的网络连接'
    } else {
      message = '发送请求失败'
    }

    Message({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
